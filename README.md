# 足球队报名与数据统计系统

## 当前版本：v1.0.0
最新更新：完成完整系统开发，集成Supabase数据库

### 1. 管理员系统
- 管理员登录认证（基于Supabase Auth）
- 安全的用户会话管理
- 登录状态持久化
- 权限控制和访问管理

### 2. 周报名管理
- 球员每周比赛报名
- 手动分队功能（白队/蓝队）
- 队长选择和管理
- 已报名球员实时显示
- 比赛类型选择（内部赛/对外赛）
- 对手队伍信息录入

### 3. 比赛数据记录
- 完整的比赛信息录入
- 实时比分记录
- 详细的球员统计（进球/助攻）
- 队伍阵容管理
- 比赛数据验证和保存
- 球员移除功能

### 4. 数据统计与分析
- 多维度球员统计排行
- 出勤率统计和排名
- 进球榜（总榜/年度榜）
- 助攻榜（总榜/年度榜）
- 历史比赛数据查询
- 实时数据更新显示

## 项目结构

### 1. 首页 (index.html)
- 管理员登录界面
- 最新比赛数据展示
- 球员统计排行榜（出勤/进球/助攻 TOP5）
- 快速导航菜单
- 用户会话状态管理

### 2. 管理员登录 (login.html)
- Supabase身份认证
- 安全登录表单
- 错误处理和用户反馈
- 登录成功后自动跳转

### 3. 报名页面 (signup.html)
- 球员报名表单
- 手动分队功能
- 队长选择界面
- 比赛类型配置
- 实时队伍显示
- 数据本地存储和云端同步

### 4. 数据记录页面 (record.html)
- 比赛基本信息设置
- 实时比分录入
- 球员统计数据录入（进球/助攻）
- 队伍阵容管理
- 数据验证和提交
- 比赛结果汇总

### 5. 比赛结果页面 (results.html)
- 历史比赛列表
- 比赛详情查看
- 比赛数据编辑功能
- 比赛删除确认
- 数据筛选和搜索

### 6. 数据统计页面 (stats.html)
- 出勤率排行榜
- 进球榜统计
- 助攻榜统计
- 年度数据筛选
- 动态数据加载

## 技术架构

### 前端技术
- **HTML5**: 语义化页面结构
- **CSS3**: 响应式布局设计
- **原生JavaScript**: 核心业务逻辑
- **Flexbox/Grid**: 现代布局方案
- **LocalStorage**: 本地数据缓存

### 后端服务
- **Supabase**: Backend-as-a-Service平台
- **PostgreSQL**: 关系型数据库
- **Supabase Auth**: 用户认证服务
- **实时数据同步**: 自动数据更新

### 数据库设计
- `matches`: 比赛基本信息表
- `match_players`: 比赛球员关联表
- `match_captains`: 队长信息表
- `match_stats`: 比赛统计数据表
- `regular_player_stats_2025`: 球员年度统计表
- `players`: 球员基础信息表

### 核心功能特性
- **手动分队系统**: 管理员可灵活分配队伍成员
- **实时数据同步**: 云端数据实时更新
- **移动端适配**: 响应式设计支持多设备
- **数据完整性**: 完善的数据验证机制
- **用户体验优化**: 流畅的交互设计

## 设计规范

### 配色方案
- **主色调**: #1E88E5（蓝色）- 导航和主要按钮
- **辅助色**: #43A047（绿色）- 成功状态和确认操作
- **强调色**: #FFC107（黄色）- 重要提示和警告
- **背景色**: #F5F5F5（浅灰）- 页面背景
- **文字色**: #333333（深灰）- 主要文本内容

### 部署信息
- **开发环境**: 本地开发服务器
- **生产环境**: Vercel部署
- **数据库**: Supabase云端托管
- **版本控制**: Git版本管理

### 系统特点
- ✅ 完整的用户认证系统
- ✅ 灵活的队伍分配管理
- ✅ 实时数据统计
- ✅ 移动端友好设计
- ✅ 数据安全保障
- ✅ 可扩展架构设计