<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>足球队报名与数据统计系统</title>
    <link rel="stylesheet" href="styles.css?v=2.5">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .dashboard {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 2rem;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 登录区域样式 */
        .login-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: bold;
            color: #333;
        }

        .form-group input {
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .login-btn {
            background: #1E88E5;
            color: white;
            padding: 1rem;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .login-btn:hover {
            background: #1565C0;
        }

        .error-message {
            color: #dc3545;
            background: #f8d7da;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            display: none;
        }

        /* 数据展示区域样式 */
        .stats-section {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .latest-match, .top-players {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .section-title {
            color: #1E88E5;
            font-size: 1.4rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #1E88E5;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .match-card {
            background: linear-gradient(to bottom, #ffffff, #f8f9fa);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #eee;
        }

        .match-header {
            background: linear-gradient(135deg, #1E88E5, #1565C0);
            color: white;
            padding: 1rem;
        }

        .match-content {
            padding: 1.5rem;
        }

        .match-score {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1.5rem 0;
        }

        .team-score {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .team-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .score {
            font-size: 2rem;
            font-weight: bold;
            color: #1E88E5;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
        }

        .stats-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .stats-card h4 {
            color: #1E88E5;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            text-align: center;
        }

        .stats-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .stats-list li {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }

        .stats-list li:last-child {
            border-bottom: none;
        }

        .player-rank {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .rank-number {
            background: #1E88E5;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            font-weight: bold;
        }

        /* 响应式布局 */
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                padding: 1rem;
                gap: 1.5rem;
            }
            
            .login-section {
                padding: 1.5rem;
            }
            
            .login-section h2 {
                font-size: 1.4rem;
                text-align: center;
                margin-bottom: 1.5rem;
            }
            
            .form-group input {
                padding: 1rem;
                font-size: 1rem;
            }
            
            .login-btn {
                padding: 1.2rem;
                font-size: 1.1rem;
                min-height: 44px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .stats-card {
                padding: 1.2rem;
            }
            
            .stats-card h4 {
                font-size: 1rem;
                margin-bottom: 0.8rem;
            }
            
            .stats-list li {
                padding: 0.8rem 0;
                font-size: 0.95rem;
            }
            
            .rank-number {
                width: 28px;
                height: 28px;
                font-size: 1rem;
            }
            
            .match-card {
                margin-bottom: 1.5rem;
            }
            
            .match-header {
                padding: 1rem;
                flex-direction: column;
                gap: 0.8rem;
                text-align: center;
            }
            
            .match-info {
                justify-content: center;
                gap: 0.5rem;
            }
            
            .match-content {
                padding: 1.2rem;
            }
            
            .match-score {
                padding: 1rem 0;
            }
            
            .team-name {
                font-size: 1.1rem;
            }
            
            .score {
                font-size: 1.8rem;
            }
            
            .team-details {
                margin-bottom: 1.2rem;
            }
            
            .team-stats {
                grid-template-columns: 1fr;
                gap: 0.8rem;
            }
            
            .stats-item {
                padding: 0.8rem;
            }
            
            .players-list {
                gap: 0.3rem;
            }
            
            .player-tag {
                padding: 0.4rem 0.6rem;
                font-size: 0.85rem;
            }
        }
        
        /* 平板端优化 */
        @media (min-width: 769px) and (max-width: 1024px) {
            .dashboard {
                grid-template-columns: 1fr;
                max-width: 800px;
                gap: 1.8rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 最新比赛详细信息样式 */
        .match-details {
            margin-top: 1.5rem;
            border-top: 1px solid #eee;
            padding-top: 1.5rem;
        }

        .team-details {
            margin-bottom: 1.5rem;
        }

        .team-players {
            margin-bottom: 1rem;
        }

        .team-players h4 {
            color: #1E88E5;
            margin-bottom: 0.8rem;
            font-size: 1rem;
        }

        .players-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .player-tag {
            background: #f8f9fa;
            padding: 0.3rem 0.8rem;
            border-radius: 16px;
            font-size: 0.9rem;
            color: #333;
        }

        .team-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }

        .stats-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
        }

        .stats-item h5 {
            color: #666;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .stats-item div {
            font-size: 0.9rem;
            margin: 0.3rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">丛莱梅蔬果足球队</div>
        <ul class="nav-menu">
            <li><a href="index.html" class="active">首页</a></li>
            <li><a href="signup.html">比赛报名</a></li>
            <li><a href="results.html">比赛结果</a></li>
            <li><a href="stats.html">球员统计</a></li>
            <li><a href="winrate.html">胜率统计</a></li>
        </ul>
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </nav>

    <main class="dashboard">
        <!-- 登录区域 -->
        <section class="login-section">
            <h2>管理员登录</h2>
            <div id="errorMessage" class="error-message"></div>
            <form class="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="email">邮箱</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" required>
                </div>
                <button type="submit" class="login-btn">登录</button>
            </form>
        </section>

        <!-- 数据展示区域 -->
        <section class="stats-section">
            <div class="latest-match">
                <h3 class="section-title">
                    <i class="fas fa-futbol"></i>
                    最新比赛
                </h3>
                <div id="latestMatch"></div>
            </div>
            
            <div class="top-players">
                <h3 class="section-title">
                    <i class="fas fa-trophy"></i>
                    球员榜单
                </h3>
                <div class="stats-grid">
                    <div class="stats-card">
                        <h4>出勤榜</h4>
                        <ul class="stats-list" id="topAttendance"></ul>
                    </div>
                    <div class="stats-card">
                        <h4>射手榜</h4>
                        <ul class="stats-list" id="topScorers"></ul>
                    </div>
                    <div class="stats-card">
                        <h4>助攻榜</h4>
                        <ul class="stats-list" id="topAssists"></ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <a href="index.html" class="mobile-nav-item active">
            <div class="mobile-nav-icon"><i class="fas fa-home"></i></div>
            <div class="mobile-nav-text">首页</div>
        </a>
        <a href="results.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-trophy"></i></div>
            <div class="mobile-nav-text">结果</div>
        </a>
        <a href="stats.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-chart-bar"></i></div>
            <div class="mobile-nav-text">统计</div>
        </a>
        <a href="winrate.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-percentage"></i></div>
            <div class="mobile-nav-text">胜率</div>
        </a>
    </nav>

    <footer class="footer">
        <p>© 2013-2025 丛莱梅蔬果足球队</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/dist/umd/supabase.min.js"></script>
    <script>
        const supabaseClient = supabase.createClient(
            'https://obidukxlcgecpooynqnz.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9iaWR1a3hsY2dlY3Bvb3lucW56Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQyNjAyMDgsImV4cCI6MjA0OTgzNjIwOH0.a4Y-nWWxb8ClbMO2BUXG2vTJMqTJe2rQAXdWyKHZlHs'
        );

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', async function() {
            await Promise.all([
                loadLatestMatch(),
                loadTopPlayers()
            ]);

            // 检查登录状态
            const { data: { session } } = await supabaseClient.auth.getSession();
            if (session) {
                showLoggedInState(session.user.email);
            }
        });

        // 处理登录
        async function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');

            try {
                const { data, error } = await supabaseClient.auth.signInWithPassword({
                    email,
                    password
                });

                if (error) throw error;

                // 登录成功
                localStorage.setItem('isAdmin', 'true');
                localStorage.setItem('adminSession', data.session.access_token);
                showLoggedInState(email);
            } catch (error) {
                console.error('登录失败:', error);
                errorMessage.style.display = 'block';
                errorMessage.textContent = '登录失败：' + error.message;
            }
        }

        // 显示已登录状态
        function showLoggedInState(email) {
            const loginSection = document.querySelector('.login-section');
            loginSection.innerHTML = `
                <h2>管理员面板</h2>
                <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                    <a href="signup.html" class="login-btn" style="flex: 1; text-decoration: none; text-align: center;">
                        记录数据
                    </a>
                    <button onclick="handleLogout()" class="login-btn" style="flex: 1; background: #dc3545;">
                        退出
                    </button>
                </div>
            `;
        }

        // 加载最新比赛
        async function loadLatestMatch() {
            try {
                // 获取最新比赛
                const { data: match, error: matchError } = await supabaseClient
                    .from('matches')
                    .select('*')
                    .eq('status', 'completed')
                    .order('match_date', { ascending: false })
                    .limit(1)
                    .single();

                if (matchError) throw matchError;

                if (match) {
                    // 获取这场比赛的球员
                    const { data: players, error: playersError } = await supabaseClient
                        .from('match_players')
                        .select('*')
                        .eq('match_id', match.id);

                    if (playersError) throw playersError;

                    // 获取这场比赛的统计数据
                    const { data: stats, error: statsError } = await supabaseClient
                        .from('match_stats')
                        .select('*')
                        .eq('match_id', match.id);

                    if (statsError) throw statsError;

                    // 分离白队和蓝队数据
                    const whitePlayers = players.filter(p => p.team === 'white');
                    const bluePlayers = players.filter(p => p.team === 'blue');
                    const whiteStats = stats.filter(s => s.team === 'white');
                    const blueStats = stats.filter(s => s.team === 'blue');

                    document.getElementById('latestMatch').innerHTML = `
                        <div class="match-card">
                            <div class="match-header">
                                <div style="font-size: 1.2rem; font-weight: bold;">
                                    ${formatDate(match.match_date)}
                                </div>
                                <div style="font-size: 0.9rem; margin-top: 0.5rem;">
                                    ${match.match_type} | ${match.venue}
                                </div>
                            </div>
                            <div class="match-content">
                                <div class="match-score">
                                    <div class="team-score">
                                        <span class="team-name">白队</span>
                                        <span class="score">${match.white_score || 0}</span>
                                    </div>
                                    <div style="color: #666; margin: 0 1rem;">VS</div>
                                    <div class="team-score">
                                        <span class="score">${match.blue_score || 0}</span>
                                        <span class="team-name">蓝队</span>
                                    </div>
                                </div>
                                
                                <div class="match-details">
                                    <div class="team-details">
                                        <div class="team-players">
                                            <h4>白队阵容</h4>
                                            <div class="players-list">
                                                ${whitePlayers.map(p => 
                                                    `<span class="player-tag">${p.player_name}</span>`
                                                ).join('')}
                                            </div>
                                        </div>
                                        <div class="team-stats">
                                            <div class="stats-item">
                                                <h5>进球</h5>
                                                ${whiteStats.filter(s => s.goals > 0)
                                                    .map(s => `<div>${s.player_name} ${s.goals}球</div>`)
                                                    .join('') || '<div>无</div>'}
                                            </div>
                                            <div class="stats-item">
                                                <h5>助攻</h5>
                                                ${whiteStats.filter(s => s.assists > 0)
                                                    .map(s => `<div>${s.player_name} ${s.assists}次</div>`)
                                                    .join('') || '<div>无</div>'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="team-details">
                                        <div class="team-players">
                                            <h4>蓝队阵容</h4>
                                            <div class="players-list">
                                                ${bluePlayers.map(p => 
                                                    `<span class="player-tag">${p.player_name}</span>`
                                                ).join('')}
                                            </div>
                                        </div>
                                        <div class="team-stats">
                                            <div class="stats-item">
                                                <h5>进球</h5>
                                                ${blueStats.filter(s => s.goals > 0)
                                                    .map(s => `<div>${s.player_name} ${s.goals}球</div>`)
                                                    .join('') || '<div>无</div>'}
                                            </div>
                                            <div class="stats-item">
                                                <h5>助攻</h5>
                                                ${blueStats.filter(s => s.assists > 0)
                                                    .map(s => `<div>${s.player_name} ${s.assists}次</div>`)
                                                    .join('') || '<div>无</div>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('加载最新比赛失败:', error);
            }
        }

        // 加载球员排行
        async function loadTopPlayers() {
            try {
                // 获取所有比赛统计数据
                const { data: stats, error: statsError } = await supabaseClient
                    .from('match_stats')
                    .select('*');

                if (statsError) throw statsError;

                // 获取所有比赛球员数据（用于统计出勤）
                const { data: matchPlayers, error: playersError } = await supabaseClient
                    .from('match_players')
                    .select('*');

                if (playersError) throw playersError;

                // 计算出勤次数
                const attendanceStats = {};
                matchPlayers.forEach(record => {
                    if (!attendanceStats[record.player_name]) {
                        attendanceStats[record.player_name] = 0;
                    }
                    attendanceStats[record.player_name]++;
                });

                // 转换为数组并排序
                const attendance = Object.entries(attendanceStats)
                    .map(([name, count]) => ({ name, count }))
                    .sort((a, b) => b.count - a.count)
                    .slice(0, 5);

                // 显示出勤榜
                document.getElementById('topAttendance').innerHTML = attendance
                    .map((player, index) => `
                        <li>
                            <div class="player-rank">
                                <span class="rank-number">${index + 1}</span>
                                ${player.name}
                            </div>
                            <span>${player.count}场</span>
                        </li>
                    `)
                    .join('');

                // 计算进球和助攻统计
                // 计算每个球员的总进球和助攻
                const playerStats = {};
                stats.forEach(stat => {
                    if (!playerStats[stat.player_name]) {
                        playerStats[stat.player_name] = { goals: 0, assists: 0 };
                    }
                    playerStats[stat.player_name].goals += stat.goals || 0;
                    playerStats[stat.player_name].assists += stat.assists || 0;
                });

                // 转换为数组并排序
                const players = Object.entries(playerStats).map(([name, stats]) => ({
                    name,
                    goals: stats.goals,
                    assists: stats.assists
                }));

                // 显示进球榜
                const topScorers = players
                    .sort((a, b) => b.goals - a.goals)
                    .slice(0, 5);
                
                document.getElementById('topScorers').innerHTML = topScorers
                    .map((player, index) => `
                        <li>
                            <div class="player-rank">
                                <span class="rank-number">${index + 1}</span>
                                ${player.name}
                            </div>
                            <span>${player.goals}球</span>
                        </li>
                    `)
                    .join('');

                // 显示助攻榜
                const topAssists = players
                    .sort((a, b) => b.assists - a.assists)
                    .slice(0, 5);
                
                document.getElementById('topAssists').innerHTML = topAssists
                    .map((player, index) => `
                        <li>
                            <div class="player-rank">
                                <span class="rank-number">${index + 1}</span>
                                ${player.name}
                            </div>
                            <span>${player.assists}次</span>
                        </li>
                    `)
                    .join('');

            } catch (error) {
                console.error('加载球员排行失败:', error);
            }
        }

        // 处理登出
        async function handleLogout() {
            try {
                const { error } = await supabaseClient.auth.signOut();
                if (error) throw error;
                
                localStorage.removeItem('isAdmin');
                localStorage.removeItem('adminSession');
                window.location.reload();
            } catch (error) {
                console.error('退出登录失败:', error);
                alert('退出登录失败：' + error.message);
            }
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
        }

        // 汉堡菜单切换
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const navMenu = document.querySelector('.nav-menu');
            
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        }

        // 点击菜单项时关闭菜单
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-menu a');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    const hamburger = document.querySelector('.hamburger');
                    const navMenu = document.querySelector('.nav-menu');
                    hamburger.classList.remove('active');
                    navMenu.classList.remove('active');
                });
            });
        });

        // 点击页面其他地方时关闭菜单
         document.addEventListener('click', function(event) {
             const navbar = document.querySelector('.navbar');
             const hamburger = document.querySelector('.hamburger');
             const navMenu = document.querySelector('.nav-menu');
             
             if (!navbar.contains(event.target) && navMenu.classList.contains('active')) {
                 hamburger.classList.remove('active');
                 navMenu.classList.remove('active');
             }
         });
    </script>
</body>
</html>