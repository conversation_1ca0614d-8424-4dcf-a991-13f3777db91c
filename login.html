<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>管理员登录 - 足球队数据统计系统</title>
    <link rel="stylesheet" href="styles.css?v=2.1">
    <style>
        .login-container {
            max-width: 400px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: bold;
            color: #333;
        }

        .form-group input {
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .login-btn {
            background: #1E88E5;
            color: white;
            padding: 1rem;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .login-btn:hover {
            background: #1565C0;
        }

        .error-message {
            color: #dc3545;
            background: #f8d7da;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            display: none;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">⚽ 足球队管理系统</div>
        <ul class="nav-menu">
            <li><a href="index.html">首页</a></li>
            <li><a href="results.html">比赛结果</a></li>
            <li><a href="stats.html">球员统计</a></li>
            <li><a href="winrate.html">胜率统计</a></li>
        </ul>
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </nav>

    <main class="container">
        <div class="login-container">
            <h2>管理员登录</h2>
            <div id="errorMessage" class="error-message"></div>
            <form class="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="email">邮箱</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" required>
                </div>
                <button type="submit" class="login-btn">登录</button>
            </form>
        </div>
    </main>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <a href="index.html" class="mobile-nav-item active">
            <div class="mobile-nav-icon"><i class="fas fa-home"></i></div>
            <div class="mobile-nav-text">首页</div>
        </a>
        <a href="results.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-trophy"></i></div>
            <div class="mobile-nav-text">结果</div>
        </a>
        <a href="stats.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-chart-bar"></i></div>
            <div class="mobile-nav-text">统计</div>
        </a>
        <a href="winrate.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-percentage"></i></div>
            <div class="mobile-nav-text">胜率</div>
        </a>
    </nav>

    <footer class="footer">
        <p>© 2024 足球队报名与数据统计系统</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/dist/umd/supabase.min.js"></script>
    <script>
        const supabaseClient = supabase.createClient(
            'https://obidukxlcgecpooynqnz.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9iaWR1a3hsY2dlY3Bvb3lucW56Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQyNjAyMDgsImV4cCI6MjA0OTgzNjIwOH0.a4Y-nWWxb8ClbMO2BUXG2vTJMqTJe2rQAXdWyKHZlHs'
        );

        async function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');

            try {
                const { data, error } = await supabaseClient.auth.signInWithPassword({
                    email,
                    password
                });

                if (error) throw error;

                // 登录成功，保存登录状态
                localStorage.setItem('isAdmin', 'true');
                localStorage.setItem('adminSession', data.session.access_token);
                
                // 跳转到管理页面
                window.location.href = 'record.html';
            } catch (error) {
                console.error('登录失败:', error);
                errorMessage.style.display = 'block';
                errorMessage.textContent = '登录失败：' + error.message;
            }
        }

        // 检查是否已登录
        document.addEventListener('DOMContentLoaded', async function() {
            const { data: { session } } = await supabaseClient.auth.getSession();
            if (session) {
                window.location.href = 'record.html';
            }
        });
        
        // 汉堡菜单控制函数
        function toggleMenu() {
            const navMenu = document.querySelector('.nav-menu');
            const hamburger = document.querySelector('.hamburger');
            
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }
        
        // 点击菜单项或页面其他区域时关闭菜单
        document.addEventListener('DOMContentLoaded', function() {
            // 点击菜单项时关闭菜单
            document.querySelectorAll('.nav-menu a').forEach(link => {
                link.addEventListener('click', () => {
                    document.querySelector('.nav-menu').classList.remove('active');
                    document.querySelector('.hamburger').classList.remove('active');
                });
            });
            
            // 点击页面其他区域时关闭菜单
            document.addEventListener('click', function(e) {
                const navMenu = document.querySelector('.nav-menu');
                const hamburger = document.querySelector('.hamburger');
                
                if (!navMenu.contains(e.target) && !hamburger.contains(e.target)) {
                    navMenu.classList.remove('active');
                    hamburger.classList.remove('active');
                }
            });
        });
    </script>
</body>
</html>