<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>比赛报名 - 足球队数据统计系统</title>
    <link rel="stylesheet" href="styles.css?v=2.1">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/dist/umd/supabase.min.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-brand">⚽ 足球队管理系统</div>
        <ul class="nav-menu">
            <li><a href="index.html">首页</a></li>
            <li><a href="results.html">比赛结果</a></li>
            <li><a href="stats.html">球员统计</a></li>
            <li><a href="winrate.html">胜率统计</a></li>
        </ul>
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="container">
        <!-- 比赛信息 -->
        <section class="match-info">
            <div class="match-info-card">
                <div class="form-group">
                    <label for="matchDate">比赛日期</label>
                    <input type="date" id="matchDate" name="matchDate" required>
                </div>
                <div class="form-group">
                    <label for="matchTimeSlot">比赛时间</label>
                    <select id="matchTimeSlot" name="matchTimeSlot" required>
                        <option value="" disabled>请选择时间段</option>
                        <option value="14:00-16:00">14:00-16:00</option>
                        <option value="16:00-18:00">16:00-18:00</option>
                        <option value="18:00-20:00" selected>18:00-20:00</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="matchType">比赛性质</label>
                    <select id="matchType" name="matchType" onchange="toggleOpponentTeamField()">
                        <option value="内战">内战</option>
                        <option value="外战">外战</option>
                    </select>
                </div>
                <!-- 添加对手队伍字段 -->
                <div class="form-group" id="opponentTeamGroup" style="display: none;">
                    <label for="opponentTeam">对手队伍名称</label>
                    <input type="text" id="opponentTeam" name="opponentTeam" placeholder="请输入对手队伍名称">
                </div>
                
                <div class="form-group">
                    <label for="matchVenue">比赛场地</label>
                    <input type="text" id="matchVenue" name="matchVenue" value="机场">
                </div>
            </div>
        </section>

        <!-- 队伍展示 -->
        <section class="teams-display">
            <div class="teams-grid">
                <div class="team-box white-team">
                    <h3>白队</h3>
                    <div id="whiteTeam" class="team-list"></div>
                </div>
                <div class="team-box blue-team">
                    <h3>蓝队</h3>
                    <div id="blueTeam" class="team-list"></div>
                </div>
            </div>
        </section>

        <!-- 球员选择 -->
        <section class="player-selection">
            <div class="player-group">
                <h3>主力球员</h3>
                <div id="mainPlayerList" class="player-list"></div>
            </div>
            <div class="player-group">
                <h3>外援球员</h3>
                <div id="guestPlayerList" class="player-list"></div>
            </div>
        </section>

        <!-- 队长选择 移到最下面 -->
        <section class="captain-selection">
            <h3>选择队长</h3>
            <div class="captain-grid">
                <div class="captain-box">
                    <label>白队队长</label>
                    <select id="whiteCaptain">
                        <option value="">请选择队长</option>
                    </select>
                </div>
                <div class="captain-box">
                    <label>蓝队队长</label>
                    <select id="blueCaptain">
                        <option value="">请选择队长</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- 提交按钮 -->
        <div class="submit-section">
            <button type="button" class="btn-primary" id="submitTeams">保存分队</button>
        </div>

        <!-- 添加确认弹窗 -->
        <div id="confirmModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>确认分队结果</h3>
                </div>
                <div class="modal-body">
                    <div class="team-preview">
                        <div class="team-column">
                            <h4>白队</h4>
                            <ul id="whiteTeamPreview" class="team-preview-list"></ul>
                        </div>
                        <div class="team-column">
                            <h4>蓝队</h4>
                            <ul id="blueTeamPreview" class="team-preview-list"></ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="closeConfirmModal()">返回修改</button>
                    <button class="btn-primary" onclick="confirmTeams()">确认</button>
                </div>
            </div>
        </div>
    </main>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <a href="index.html" class="mobile-nav-item active">
            <div class="mobile-nav-icon"><i class="fas fa-home"></i></div>
            <div class="mobile-nav-text">首页</div>
        </a>
        <a href="results.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-trophy"></i></div>
            <div class="mobile-nav-text">结果</div>
        </a>
        <a href="stats.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-chart-bar"></i></div>
            <div class="mobile-nav-text">统计</div>
        </a>
        <a href="winrate.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-percentage"></i></div>
            <div class="mobile-nav-text">胜率</div>
        </a>
    </nav>

    <!-- 页脚 -->
    <footer class="footer">
        <p>© 2024 足球队报名与数据统计系统</p>
    </footer>

    <!-- 添加样式 -->
    <style>
    .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    .player-grid {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 10px;
        margin: 15px 0;
        padding: 0 15px;
    }

    .player-grid li {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        min-width: 120px;
        max-width: 160px;
    }

    .player-name {
        flex: 1;
        font-size: 16px;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 10px;
    }

    .team-buttons button {
        min-width: 35px;
        padding: 4px 10px;
        font-size: 15px;
    }

    .captain-selection {
        margin-top: 30px;
        background-color: #e3f2fd;
        border: 2px solid #1E88E5;
        border-radius: 8px;
        padding: 20px;
    }

    .captain-buttons {
        display: flex;
        justify-content: space-around;
        gap: 2rem;
        margin-top: 1rem;
    }

    .team-captain {
        flex: 1;
        text-align: center;
    }

    .captain-select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 0.5rem;
    }

    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1000;
    }

    .modal-content {
        background-color: white;
        margin: 10% auto;
        padding: 2rem;
        width: 90%;
        max-width: 800px;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .team-preview {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin: 1rem 0;
    }

    .team-preview-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .team-preview-list li {
        padding: 0.5rem;
        border-bottom: 1px solid #eee;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
    }

    .btn-secondary {
        padding: 0.5rem 1rem;
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
    }

    .team-header {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #1E88E5;
    }

    .captain-info {
        color: #1E88E5;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .captain-selection {
        background-color: #e3f2fd;  /* 更亮的蓝色背景 */
        border: 2px solid #1E88E5;  /* 添加边框 */
    }

    .captain-selection h3 {
        color: #1E88E5;
        text-align: center;
        margin-bottom: 1rem;
    }

    .team-captain h4 {
        color: #1E88E5;
    }

    .captain-select {
        border: 2px solid #1E88E5;
        font-size: 1rem;
        color: #1E88E5;
    }

    .captain-select option {
        padding: 0.5rem;
    }

    .player-grid {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 15px;
        margin: 15px 0;
        padding: 0 15px;
    }

    .player-grid li {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        min-width: 100px;
        max-width: 140px;
    }

    .player-name {
        flex: 1;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 8px;
    }

    .team-buttons {
        display: flex;
        gap: 4px;
    }

    .team-buttons button {
        min-width: 28px;
        padding: 2px 6px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background: #fff;
        cursor: pointer;
        font-size: 12px;
    }

    .team-buttons button:hover {
        background: #f5f5f5;
        border-color: #1E88E5;
        color: #1E88E5;
    }

    .team-buttons button.selected {
        background: #1E88E5;
        color: white;
        border-color: #1565C0;
    }

    .player-list, .guest-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .section-title {
        color: #1E88E5;
        margin-bottom: 15px;
        padding-left: 15px;
        font-size: 16px;
    }

    /* 队伍展示区域样式 */
    .teams-display {
        margin: 20px 0;
    }

    .teams-grid {
        display: flex;
        gap: 30px;
        margin-bottom: 20px;
    }

    .team-box {
        flex: 1;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 20px;
        min-height: 200px;
    }

    .team-box h3 {
        color: #1E88E5;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #1E88E5;
    }

    .team-list {
        display: grid;
        grid-template-columns: repeat(4, 1fr); /* 每行4个球员 */
        gap: 10px;
        padding: 10px;
    }

    .team-list li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
    }

    .team-list .player-name {
        flex: 1;
        font-size: 16px;
        font-weight: 500;
    }

    .team-list .delete-btn {
        padding: 2px 8px;
        border: none;
        background: none;
        color: #dc3545;
        cursor: pointer;
        font-size: 16px;
    }

    /* 队长选择区域样式 */
    .captain-selection {
        margin-top: 30px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 25px;
    }

    .captain-selection h3 {
        color: #1E88E5;
        text-align: center;
        margin-bottom: 20px;
        font-size: 18px;
    }

    .captain-grid {
        display: flex;
        gap: 40px;
        justify-content: center;
    }

    .captain-box {
        flex: 1;
        max-width: 300px;
    }

    .captain-box label {
        display: block;
        color: #666;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .captain-box select {
        width: 100%;
        padding: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        font-size: 14px;
        color: #333;
        background: #f8f9fa;
        transition: all 0.3s;
    }

    .captain-box select:hover {
        border-color: #1E88E5;
    }

    .captain-box select:focus {
        outline: none;
        border-color: #1E88E5;
        box-shadow: 0 0 0 2px rgba(30,136,229,0.2);
    }
    </style>

    <script>
        // 修改 Supabase 客户端初始化部分
        const { createClient } = supabase;
        const supabaseClient = createClient(
            'https://obidukxlcgecpooynqnz.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9iaWR1a3hsY2dlY3Bvb3lucW56Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQyNjAyMDgsImV4cCI6MjA0OTgzNjIwOH0.a4Y-nWWxb8ClbMO2BUXG2vTJMqTJe2rQAXdWyKHZlHs'
        );

        // 添加通用的权限验证函数（与 record.html 相同）
        async function checkAdminAuth() {
            try {
                const { data: { session } } = await supabaseClient.auth.getSession();
                if (!session) {
                    alert('请先登录管理员账号');
                    window.location.href = 'index.html';
                    return false;
                }
                return true;
            } catch (error) {
                console.error('权限检查失败:', error);
                alert('权限检查失败，请重新登录');
                window.location.href = 'index.html';
                return false;
            }
        }

        // 修改初始化球员列表的函数
        async function initializePlayers() {
            try {
                // 首先检查权限
                if (!await checkAdminAuth()) {
                    return;
                }

                // 从 Supabase 获取所有球员，按 id 排序
                const { data: players, error } = await supabaseClient
                    .from('players')
                    .select('*')
                    .order('id', { ascending: true });  // 按 id 升序排序

                if (error) throw error;

                // 分离主力和外援
                const regularPlayers = players.filter(p => p.player_type === 'regular');
                const guestPlayers = players.filter(p => p.player_type === 'guest');

                // 生成主力球员列表 HTML
                const mainPlayerList = document.getElementById('mainPlayerList');
                mainPlayerList.innerHTML = `
                    <ul class="player-grid">
                        ${regularPlayers.map(player => `
                            <li>
                                <span class="player-name">${player.name}</span>
                                <div class="team-buttons">
                                    <button onclick="handleTeamButtonClick(this, '${player.name}', 'whiteTeam')">白</button>
                                    <button onclick="handleTeamButtonClick(this, '${player.name}', 'blueTeam')">蓝</button>
                                </div>
                            </li>
                        `).join('')}
                    </ul>
                `;

                // 生成外援球员列表 HTML
                const guestPlayerList = document.getElementById('guestPlayerList');
                guestPlayerList.innerHTML = `
                    <ul class="player-grid">
                        ${guestPlayers.map(player => `
                            <li>
                                <span class="player-name">${player.name}</span>
                                <div class="team-buttons">
                                    <button onclick="handleTeamButtonClick(this, '${player.name}', 'whiteTeam')">白</button>
                                    <button onclick="handleTeamButtonClick(this, '${player.name}', 'blueTeam')">蓝</button>
                                </div>
                            </li>
                        `).join('')}
                    </ul>
                `;

            } catch (error) {
                console.error('加载球员列表失败:', error);
                alert('加载球员列表失败，请刷新页面重试');
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', initializePlayers);

        // 更新队长选择下拉框
        function updateCaptainSelects() {
            const whiteCaptainSelect = document.getElementById('whiteCaptain');
            const blueCaptainSelect = document.getElementById('blueCaptain');
            
            // 保存当前选中的队长
            const currentWhiteCaptain = whiteCaptainSelect.value;
            const currentBlueCaptain = blueCaptainSelect.value;
            
            // 获取当前队伍成员
            const whiteTeamMembers = Array.from(document.getElementById('whiteTeam').children)
                .map(li => li.querySelector('.player-name').textContent.replace('(C)', '').trim());
            const blueTeamMembers = Array.from(document.getElementById('blueTeam').children)
                .map(li => li.querySelector('.player-name').textContent.replace('(C)', '').trim());
            
            // 更新白队队长选项
            whiteCaptainSelect.innerHTML = '<option value="">请选择队长</option>' +
                whiteTeamMembers.map(player => `<option value="${player}">${player}</option>`).join('');
            
            // 更新蓝队队长选项
            blueCaptainSelect.innerHTML = '<option value="">请选择队长</option>' +
                blueTeamMembers.map(player => `<option value="${player}">${player}</option>`).join('');
            
            // 复之前选中的队长
            if (currentWhiteCaptain && whiteTeamMembers.includes(currentWhiteCaptain)) {
                whiteCaptainSelect.value = currentWhiteCaptain;
            }
            if (currentBlueCaptain && blueTeamMembers.includes(currentBlueCaptain)) {
                blueCaptainSelect.value = currentBlueCaptain;
            }
        }

        // 添加队伍选择事件处理
        function initializeCaptainSelects() {
            const whiteCaptainSelect = document.getElementById('whiteCaptain');
            const blueCaptainSelect = document.getElementById('blueCaptain');
            
            whiteCaptainSelect.addEventListener('change', function() {
                updateCaptainDisplay('whiteTeam', this.value);
            });
            
            blueCaptainSelect.addEventListener('change', function() {
                updateCaptainDisplay('blueTeam', this.value);
            });
        }

        // 更新队长显示
        function updateCaptainDisplay(teamId, captainName) {
            const teamList = document.getElementById(teamId);
            
            // 移除所有队长标记
            teamList.querySelectorAll('.player-name').forEach(span => {
                span.textContent = span.textContent.replace('(C)', '').trim();
            });
            
            // 添加新队长标记
            if (captainName) {
                const captainLi = Array.from(teamList.children).find(li => 
                    li.querySelector('.player-name').textContent.trim() === captainName
                );
                if (captainLi) {
                    const nameSpan = captainLi.querySelector('.player-name');
                    nameSpan.textContent = `${captainName}(C)`;
                }

                // 更新选择区域中的队长标记
                const mainPlayerList = document.getElementById('mainPlayerList');
                const guestPlayerList = document.getElementById('guestPlayerList');
                
                [mainPlayerList, guestPlayerList].forEach(list => {
                    if (!list) return;
                    
                    list.querySelectorAll('.player-name').forEach(span => {
                        const playerName = span.textContent.replace('(C)', '').trim();
                        if (playerName === captainName) {
                            span.textContent = `${playerName}(C)`;
                        } else {
                            span.textContent = playerName;
                        }
                    });
                });
            }
        }

        // 修改存分队结果函数
        async function saveTeams() {
            try {
                console.log('开始保存分队结果');
                
                // 检查比赛日期和时间
                const matchDate = document.getElementById('matchDate').value;
                const matchTime = document.getElementById('matchTimeSlot').value;
                
                console.log('比赛日期:', matchDate);
                console.log('比赛时间:', matchTime);
                
                if (!matchDate) {
                    alert('请选择比赛日期！');
                    document.getElementById('matchDate').focus();
                    return;
                }
                
                if (!matchTime) {
                    alert('请选择比赛时间！');
                    document.getElementById('matchTimeSlot').focus();
                    return;
                }

                // 检查是否有队员被选中
                const whiteTeamCount = document.getElementById('whiteTeam').children.length;
                const blueTeamCount = document.getElementById('blueTeam').children.length;
                
                console.log('白队人数:', whiteTeamCount);
                console.log('蓝队人数:', blueTeamCount);
                
                if (whiteTeamCount === 0 && blueTeamCount === 0) {
                    alert('请至少选择一名队员！');
                    return;
                }

                const whiteCaptain = document.getElementById('whiteCaptain').value;
                const blueCaptain = document.getElementById('blueCaptain').value;
                
                // 获取队伍成员
                const whiteTeam = Array.from(document.getElementById('whiteTeam').children)
                    .map(li => li.querySelector('.player-name').textContent.trim());
                const blueTeam = Array.from(document.getElementById('blueTeam').children)
                    .map(li => li.querySelector('.player-name').textContent.trim());
                
                console.log('白队队员:', whiteTeam);
                console.log('蓝队队员:', blueTeam);

                // 显示确认弹窗
                showConfirmModal(whiteTeam, blueTeam, whiteCaptain, blueCaptain);
                
            } catch (error) {
                console.error('保存分队结果时出错:', error);
                alert('保存失败：' + error.message);
            }
        }

        // 确保提交按钮绑定了事件
        document.addEventListener('DOMContentLoaded', function() {
            const submitButton = document.getElementById('submitTeams');
            if (submitButton) {
                submitButton.addEventListener('click', saveTeams);
                console.log('已绑定保存按钮事件');
            } else {
                console.error('找不到保存按钮');
            }
        });

        // 显示确认弹窗
        function showConfirmModal(whiteTeam, blueTeam, whiteCaptain, blueCaptain) {
            const modal = document.getElementById('confirmModal');
            const whitePreview = document.getElementById('whiteTeamPreview');
            const bluePreview = document.getElementById('blueTeamPreview');
            
            // 显示白队名单
            whitePreview.innerHTML = `
                <div class="team-header">
                    ${whiteCaptain ? `<div class="captain-info">队长：${whiteCaptain}</div>` : ''}
                </div>
                ${whiteTeam.map(player => `<li>${player}</li>`).join('')}
            `;
            
            // 显示蓝队名单
            bluePreview.innerHTML = `
                <div class="team-header">
                    ${blueCaptain ? `<div class="captain-info">队长：${blueCaptain}</div>` : ''}
                </div>
                ${blueTeam.map(player => `<li>${player}</li>`).join('')}
            `;
            
            modal.style.display = 'block';
        }

        // 关闭确认弹窗
        function closeConfirmModal() {
            document.getElementById('confirmModal').style.display = 'none';
        }

        // 确认分队
async function confirmTeams() {
    try {
        // 获取比赛基本信息
        const matchDate = document.getElementById('matchDate').value;
        const matchTime = document.getElementById('matchTimeSlot').value;
        const matchType = document.getElementById('matchType').value;
        const venue = document.getElementById('matchVenue').value;
        const opponentTeam = document.getElementById('opponentTeam').value;

        // 获取队伍信息
        const whiteTeam = Array.from(document.getElementById('whiteTeam').children)
            .map(li => li.querySelector('.player-name').textContent.trim());
        const blueTeam = Array.from(document.getElementById('blueTeam').children)
            .map(li => li.querySelector('.player-name').textContent.trim());
        const whiteCaptain = document.getElementById('whiteCaptain').value;
        const blueCaptain = document.getElementById('blueCaptain').value;

        console.log('准备创建比赛记录...');

        // 首先创建比赛记录
        const { data: match, error: matchError } = await supabaseClient
            .from('matches')
            .insert([{
                id: crypto.randomUUID(),  // 显式生成 UUID
                match_date: matchDate,
                match_time: matchTime,
                match_type: matchType,
                venue: venue,
                opponent_team: matchType === '外战' ? opponentTeam : null,
                status: 'pending',
                white_score: 0,
                blue_score: 0
            }])
            .select()
            .single();

        if (matchError) throw matchError;
        console.log('创建比赛记录成功:', match);

        // 创建球员记录（移除 is_captain 字段）
        const playerRecords = [
            ...whiteTeam.map(player => ({
                match_id: match.id,
                player_name: player,
                team: 'white'
            })),
            ...blueTeam.map(player => ({
                match_id: match.id,
                player_name: player,
                team: 'blue'
            }))
        ];

        // 保存球员记录
        const { error: playersError } = await supabaseClient
            .from('match_players')
            .insert(playerRecords);

        if (playersError) throw playersError;
        console.log('创建球员记录成功');

        // 如果有队长，创建队长记录
        if (whiteCaptain || blueCaptain) {
            const captainRecords = [];
            if (whiteCaptain) {
                captainRecords.push({
                    match_id: match.id,
                    team: 'white',
                    captain_name: whiteCaptain
                });
            }
            if (blueCaptain) {
                captainRecords.push({
                    match_id: match.id,
                    team: 'blue',
                    captain_name: blueCaptain
                });
            }

            // 保存队长信息到 match_captains 表
            const { error: captainsError } = await supabaseClient
                .from('match_captains')
                .insert(captainRecords);

            if (captainsError) throw captainsError;
            console.log('创建队长记录成功');
        }

        // 保存到 localStorage
        const currentMatch = {
            matchId: match.id,
            matchInfo: {
                date: matchDate,
                time: matchTime,
                type: matchType,
                venue: venue,
                opponentTeam: matchType === '外战' ? opponentTeam : null
            },
            teams: {
                white: {
                    players: whiteTeam,
                    captain: whiteCaptain
                },
                blue: {
                    players: blueTeam,
                    captain: blueCaptain
                }
            }
        };

        localStorage.setItem('currentMatch', JSON.stringify(currentMatch));
        console.log('保存到 localStorage 成功:', currentMatch);

        // 关闭弹窗
        closeConfirmModal();

        // 跳转到记录页面
        window.location.href = 'record.html';

    } catch (error) {
        console.error('保存比赛数据失败:', error);
        alert('保存失败: ' + error.message);
    }
}

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', initializePlayers);

        // 添加加载状态函数
        function setLoading(isLoading) {
            const submitBtn = document.querySelector('.confirm-btn');
            if (isLoading) {
                submitBtn.disabled = true;
                submitBtn.textContent = '保存中...';
            } else {
                submitBtn.disabled = false;
                submitBtn.textContent = '确认';
            }
        }

        // 处理队伍按钮点击
        function handleTeamButtonClick(button, playerName, team) {
            const playerLi = button.closest('li');
            const otherTeam = team === 'whiteTeam' ? 'blueTeam' : 'whiteTeam';
            
            // 更新按钮状态
            const buttons = playerLi.querySelectorAll('button');
            const isSelected = button.classList.contains('selected');
            
            // 果是已选中按钮，则取消选择
            if (isSelected) {
                button.classList.remove('selected');
                removePlayerFromTeam(playerName, team);
                return;
            }
            
            // 移除之前的选中状态
            buttons.forEach(btn => btn.classList.remove('selected'));
            button.classList.add('selected');
            
            // 从另一个队伍移除
            removePlayerFromTeam(playerName, otherTeam);

            // 添加到中的队伍
            addPlayerToTeam(playerName, team, playerLi);
        }

        // 从队伍中移除球员
        function removePlayerFromTeam(playerName, teamId) {
            const teamList = document.getElementById(teamId);
            if (!teamList) return;
            
            const playerToRemove = Array.from(teamList.children).find(li => 
                li.textContent.trim().startsWith(playerName)
            );
            
            if (playerToRemove) {
                playerToRemove.remove();
                // 更新队长选择下拉框
                updateCaptainSelects();
            }
        }

        // 添加球员到队伍
        function addPlayerToTeam(playerName, teamId) {
            const teamList = document.getElementById(teamId);
            if (!teamList) return;
            
            // 检查是否已经在队伍中
            const existingPlayer = Array.from(teamList.children).find(li => 
                li.textContent.trim().replace('(C)', '').replace('×', '').trim() === playerName
            );
            
            if (!existingPlayer) {
                const teamLi = document.createElement('li');
                teamLi.innerHTML = `
                    <span class="player-name">${playerName}</span>
                    <button class="delete-btn" title="移除" onclick="removePlayerFromTeam('${playerName}', '${teamId}')">×</button>
                `;
                teamList.appendChild(teamLi);
                
                // 更新队长选择下拉框
                updateCaptainSelects();
            }
        }

        // 初始化队长选择事件
        function initializeCaptainSelects() {
            const whiteCaptainSelect = document.getElementById('whiteCaptain');
            const blueCaptainSelect = document.getElementById('blueCaptain');
            
            whiteCaptainSelect.addEventListener('change', function() {
                updateCaptainDisplay('whiteTeam', this.value);
            });
            
            blueCaptainSelect.addEventListener('change', function() {
                updateCaptainDisplay('blueTeam', this.value);
            });
        }

        // 添加这个函数来切换对手队伍输入框的显示状态
        function toggleOpponentTeamField() {
            const matchType = document.getElementById('matchType').value;
            const opponentTeamGroup = document.getElementById('opponentTeamGroup');
            
            if (matchType === '外战') {
                opponentTeamGroup.style.display = 'block';
            } else {
                opponentTeamGroup.style.display = 'none';
                document.getElementById('opponentTeam').value = '';
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCaptainSelects();
            toggleOpponentTeamField(); // 初始化对手队伍字段显示状态
        });
        
        // 汉堡菜单控制函数
        function toggleMenu() {
            const navMenu = document.querySelector('.nav-menu');
            const hamburger = document.querySelector('.hamburger');
            
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }
        
        // 点击菜单项或页面其他区域时关闭菜单
        document.addEventListener('DOMContentLoaded', function() {
            // 点击菜单项时关闭菜单
            document.querySelectorAll('.nav-menu a').forEach(link => {
                link.addEventListener('click', () => {
                    document.querySelector('.nav-menu').classList.remove('active');
                    document.querySelector('.hamburger').classList.remove('active');
                });
            });
            
            // 点击页面其他区域时关闭菜单
            document.addEventListener('click', function(e) {
                const navMenu = document.querySelector('.nav-menu');
                const hamburger = document.querySelector('.hamburger');
                
                if (!navMenu.contains(e.target) && !hamburger.contains(e.target)) {
                    navMenu.classList.remove('active');
                    hamburger.classList.remove('active');
                }
            });
        });
    </script>
</body>
</html>