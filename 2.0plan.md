# 胜率统计功能开发计划 v2.0

## 项目概述
基于Supabase数据库中的足球比赛记录，开发球员胜率统计功能。采用预计算策略，在比赛结束后计算并存储胜率数据，提高查询性能。

## 开发策略
**分两阶段实施：**
1. **阶段一**：开发独立的胜率计算网页
2. **阶段二**：将功能集成到现有stats.html页面

---

## 阶段一：独立网页开发

### 1. 数据库设计
#### 1.1 创建胜率统计表 `player_winrate_2025`  //已完成
sql
CREATE TABLE player_winrate_2025 (
    id SERIAL PRIMARY KEY,
    player_name TEXT NOT NULL,
    total_matches INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    draws INTEGER DEFAULT 0,
    losses INTEGER DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0.00,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(player_name)
);

#### 1.2 数据字段说明  //已完成
- player_name : 球员姓名
- total_matches : 内战总场次
- wins : 胜利场次
- draws : 平局场次
- losses : 失败场次
- win_rate : 胜率（胜场/总场次 * 100）
- last_updated : 最后更新时间

### 2. 核心计算逻辑开发 
#### 2.1 胜率计算函数  //已完成
- 输入 : 2025年所有内战比赛数据
- 处理逻辑 :
  ```
  FOR 每个球员:
    1. 查询match_players表获取参赛记录
    2. 关联matches表获取比分信息
    3. 判定胜负:
       - 球员在white队: white_score > 
       blue_score = 胜利
       - 球员在blue队: blue_score > 
       white_score = 胜利
       - 比分相等 = 平局
       - 其他 = 失败
    4. 统计各项数据
    5. 计算胜率 = wins / total_matches * 100
  ``` 
#### 2.2 数据更新策略
- 触发时机 : 比赛数据录入完成后
- 更新方式 : 全量重新计算（考虑到数据量不大）
- 备选方案 : 增量更新（后续优化）

### 3. 独立网页开发 
####3.1 创建 winrate.html
- 页面结构 : 参考现有stats.html设计
- 功能模块 :
  - 数据计算按钮
  - 胜率排行榜显示
  - 数据刷新功能
  - 计算进度提示 
#### 3.2 创建 js/winrate.js
- 核心功能 :
  - 连接Supabase数据库
  - 执行胜率计算逻辑
  - 更新player_winrate_2025表
  - 显示计算结果
  - 错误处理和用户反馈 
#### 3.3 页面功能清单
- 手动触发胜率计算
- 显示计算进度
- 胜率排行榜展示
- 支持按胜率/总场次排序
- 显示详细统计信息（总场次、胜平负）
- 数据导出功能（可选）
### 4. 测试验证 
#### 4.1 数据准确性测试
- 手动验证几个球员的胜率计算
- 检查边界情况（0场比赛、全胜、全负）
- 验证平局处理逻辑 
#### 4.2 性能测试
- 测试大量数据的计算时间
- 验证数据库写入性能
## 阶段二：功能集成
### 5. 集成到现有统计页面 
#### 5.1 修改 stats.html

- 添加第四个tab "胜率榜"
- 调整页面布局适应新tab 
#### 5.2 修改 js/stats.js

- 添加胜率数据加载逻辑
- 集成胜率榜显示功能
- 保持与现有三个榜单的一致性 
#### 5.3 自动更新机制
- 在比赛数据录入后自动触发胜率计算
- 集成到现有的数据刷新流程
### 6. 用户体验优化 
#### 6.1 界面优化
- 统一设计风格
- 响应式布局适配
- 加载状态提示 
#### 6.2 功能完善
- 最小场次过滤选项
- 时间范围选择
- 数据更新时间显示