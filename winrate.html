<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>胜率统计 - 足球队数据统计系统</title>
    <link rel="stylesheet" href="styles.css?v=2.1">
    <style>
        .winrate-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .winrate-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .winrate-header h1 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .control-panel {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
            justify-content: center;
        }

        .control-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }

        .control-btn:hover:not(:disabled) {
            background: #2980b9;
        }

        .control-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .control-btn.success {
            background: #27ae60;
        }

        .control-btn.danger {
            background: #e74c3c;
        }

        .progress-message {
            padding: 0.75rem 1rem;
            border-radius: 5px;
            font-weight: 500;
            min-width: 200px;
            text-align: center;
        }

        .progress-message.info {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .progress-message.success {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .progress-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .filter-panel {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-group {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group label {
            font-weight: 500;
            color: #2c3e50;
        }

        .filter-group select,
        .filter-group input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .winrate-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card h3 {
            margin: 0 0 0.5rem 0;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .stat-card .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
        }

        .winrate-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #34495e;
            color: white;
            display: grid;
            grid-template-columns: 60px 1fr 80px 80px 80px 80px 100px 120px;
            gap: 1rem;
            padding: 1rem;
            font-weight: 500;
        }

        .table-header .col {
            cursor: pointer;
            user-select: none;
            transition: background-color 0.2s;
            padding: 0.25rem;
            border-radius: 4px;
        }

        .table-header .col:hover {
            background: rgba(255,255,255,0.1);
        }

        .table-header .col.sorted {
            background: rgba(255,255,255,0.2);
        }

        .table-header .col.sorted::after {
            content: ' ↓';
        }

        .table-header .col.sorted.asc::after {
            content: ' ↑';
        }

        .player-row {
            display: grid;
            grid-template-columns: 60px 1fr 80px 80px 80px 80px 100px 120px;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid #ecf0f1;
            align-items: center;
        }

        .player-row:nth-child(even) {
            background: #f8f9fa;
        }

        .player-row:hover {
            background: #e8f4fd;
        }

        .rank {
            text-align: center;
            font-weight: bold;
        }



        .player-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .player-type {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .player-type.regular {
            background: #d4edda;
            color: #155724;
        }

        .player-type.guest {
            background: #fff3cd;
            color: #856404;
        }

        .winrate-value {
            font-weight: bold;
        }

        .winrate-high {
            color: #27ae60;
        }

        .winrate-medium {
            color: #f39c12;
        }

        .winrate-low {
            color: #e74c3c;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 1rem;
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        /* 移动端卡片布局 */
        .mobile-player-cards {
            display: none;
            padding: 0 8px;
        }
        
        .player-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
            position: relative;
            overflow: hidden;
        }
        
        .player-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #e0e0e0;
        }
        

        
        .player-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .player-card-rank {
            background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
            color: #666;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            min-width: 32px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        

        
        .player-card-name {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            flex: 1;
            margin: 0 16px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        
        .player-card-type {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .player-card-type.regular {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
            border: 1px solid #90caf9;
        }
        
        .player-card-type.guest {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
            color: #e65100;
            border: 1px solid #ffb74d;
        }
        
        .player-card-stats {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .stat-item {
            flex: 1;
            text-align: center;
            padding: 12px 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border: 1px solid #dee2e6;
        }
        
        .stat-item.highlight {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid #4caf50;
            box-shadow: 0 2px 8px rgba(76,175,80,0.2);
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 6px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-value.winrate.winrate-high {
            color: #27ae60;
            text-shadow: 0 1px 2px rgba(39,174,96,0.2);
        }
        
        .stat-value.winrate.winrate-medium {
            color: #f39c12;
            text-shadow: 0 1px 2px rgba(243,156,18,0.2);
        }
        
        .stat-value.winrate.winrate-low {
            color: #e74c3c;
            text-shadow: 0 1px 2px rgba(231,76,60,0.2);
        }
        
        .player-card-details {
            display: flex;
            justify-content: space-around;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 12px 8px;
            border: 1px solid #dee2e6;
        }
        
        .detail-item {
            text-align: center;
            flex: 1;
        }
        
        .detail-label {
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 4px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }
        
        .detail-value {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .winrate-container {
                padding: 1rem;
            }
            
            .control-panel {
                flex-direction: column;
                gap: 1rem;
            }

            .winrate-table {
                display: none;
            }
            
            .mobile-player-cards {
                display: block;
            }
            
            /* 移动端统计卡片优化 */
            .winrate-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
                margin-bottom: 1.5rem;
            }
            
            .stat-card {
                padding: 1rem;
                border-radius: 6px;
            }
            
            .stat-card h3 {
                font-size: 0.8rem;
                margin-bottom: 0.3rem;
            }
            
            .stat-card .stat-value {
                font-size: 1.5rem;
            }
        }
        
        @media (min-width: 769px) {
            .mobile-player-cards {
                display: none;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">丛莱梅蔬果足球队</div>
        <ul class="nav-menu">
            <li><a href="index.html">首页</a></li>
            <li><a href="results.html">比赛结果</a></li>
            <li><a href="stats.html">球员统计</a></li>
            <li><a href="winrate.html" class="active">胜率统计</a></li>
        </ul>
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </nav>
    
    <main class="container">
        <div class="winrate-container">
        
        <div class="winrate-header">
            <h1>球员胜率统计</h1>
            <p>2025年内战胜率数据分析</p>
        </div>

        <div class="control-panel">
            <button id="calculate-winrate-btn" class="control-btn">计算胜率</button>
            <button id="refresh-data-btn" class="control-btn">刷新数据</button>
            <div id="winrate-progress" class="progress-message info">点击"计算胜率"开始更新数据</div>
        </div>

        <div class="filter-panel">
            <div class="filter-group">
                <label for="min-matches">最少参赛场次:</label>
                <select id="min-matches">
                    <option value="0">不限制</option>
                    <option value="3" selected>3场及以上</option>
                    <option value="5">5场及以上</option>
                    <option value="10">10场及以上</option>
                </select>
                
                <label for="player-type-filter">球员类型:</label>
                <select id="player-type-filter">
                    <option value="all">全部</option>
                    <option value="regular" selected>常规球员</option>
                    <option value="guest">外援</option>
                </select>
                
                <label for="sort-field">排序方式:</label>
                <select id="sort-field">
                    <option value="win_rate_desc">按胜率(高到低)</option>
                    <option value="win_rate_asc">按胜率(低到高)</option>
                    <option value="total_matches_desc">按场次(多到少)</option>
                    <option value="total_matches_asc">按场次(少到多)</option>
                    <option value="wins_desc">按胜场(多到少)</option>
                    <option value="wins_asc">按胜场(少到多)</option>
                </select>
            </div>
        </div>

        <div id="winrate-stats" class="winrate-stats">
            <!-- 统计卡片将在这里动态生成 -->
        </div>

        <div id="winrate-table-container" class="winrate-table">
            <div class="no-data">请先计算胜率数据</div>
        </div>
        
        <!-- 移动端卡片容器 -->
        <div id="mobile-cards-container" class="mobile-player-cards">
            <!-- 移动端卡片将在这里动态生成 -->
        </div>
    </main>
    
    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <a href="index.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-home"></i></div>
            <div class="mobile-nav-text">首页</div>
        </a>
        <a href="results.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-trophy"></i></div>
            <div class="mobile-nav-text">结果</div>
        </a>
        <a href="stats.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-chart-bar"></i></div>
            <div class="mobile-nav-text">统计</div>
        </a>
        <a href="winrate.html" class="mobile-nav-item active">
            <div class="mobile-nav-icon"><i class="fas fa-percentage"></i></div>
            <div class="mobile-nav-text">胜率</div>
        </a>
    </nav>
    
    <footer class="footer">
        <p>© 2013-2025 丛莱梅蔬果足球队</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/dist/umd/supabase.min.js"></script>
    <script src="js/winrate.js"></script>
    <script>
        // 等待 DOM 和 Supabase 都加载完成
        window.addEventListener('load', function() {
            if (typeof supabase === 'undefined') {
                console.error('Supabase 未加载');
                return;
            }
            console.log('Supabase 已加载');
            window.winrateCalculator = new WinrateCalculator();
            
            // 绑定按钮事件
            document.getElementById('calculate-winrate-btn').addEventListener('click', async () => {
                await window.winrateCalculator.calculateWinrates();
                await loadAndDisplayData();
            });
            
            document.getElementById('refresh-data-btn').addEventListener('click', loadAndDisplayData);
            
            // 绑定筛选器事件
            document.getElementById('min-matches').addEventListener('change', loadAndDisplayData);
            document.getElementById('player-type-filter').addEventListener('change', loadAndDisplayData);
            document.getElementById('sort-field').addEventListener('change', loadAndDisplayData);
            
            // 初始加载数据
            loadAndDisplayData();
        });
        
        // 加载并显示数据
        async function loadAndDisplayData() {
            try {
                const data = await window.winrateCalculator.getAllWinrateData();
                
                if (data.length === 0) {
                    showNoData();
                    return;
                }
                
                await displayStats(data);
                displayTable(data);
                
            } catch (error) {
                console.error('加载数据失败:', error);
                document.getElementById('winrate-progress').textContent = `加载失败: ${error.message}`;
                document.getElementById('winrate-progress').className = 'progress-message error';
            }
        }
        
        // 显示统计卡片
        async function displayStats(data) {
            const totalPlayers = data.length;
            const regularPlayers = data.filter(p => p.player_type === 'regular').length;
            const guestPlayers = data.filter(p => p.player_type === 'guest').length;
            
            // 获取真实的内战总数
            let totalInternalMatches = 0;
            try {
                totalInternalMatches = await window.winrateCalculator.getTotalInternalMatches();
            } catch (error) {
                console.error('获取内战总数失败:', error);
            }
            
            const statsHtml = `
                <div class="stat-card">
                    <h3>总球员数</h3>
                    <div class="stat-value">${totalPlayers}</div>
                </div>
                <div class="stat-card">
                    <h3>常规球员</h3>
                    <div class="stat-value">${regularPlayers}</div>
                </div>
                <div class="stat-card">
                    <h3>外援</h3>
                    <div class="stat-value">${guestPlayers}</div>
                </div>
                <div class="stat-card">
                    <h3>内战数量</h3>
                    <div class="stat-value">${totalInternalMatches}</div>
                </div>
            `;
            
            document.getElementById('winrate-stats').innerHTML = statsHtml;
        }
        
        // 显示表格
        function displayTable(allData) {
            // 应用筛选器
            const minMatches = parseInt(document.getElementById('min-matches').value);
            const playerTypeFilter = document.getElementById('player-type-filter').value;
            const sortField = document.getElementById('sort-field').value;
            
            let filteredData = allData.filter(player => {
                if (player.total_matches < minMatches) return false;
                if (playerTypeFilter !== 'all' && player.player_type !== playerTypeFilter) return false;
                return true;
            });
            
            // 排序
            const isDesc = sortField.endsWith('_desc');
            const field = sortField.replace(/_desc$|_asc$/, '');
            
            filteredData.sort((a, b) => {
                const valueA = parseFloat(a[field]);
                const valueB = parseFloat(b[field]);
                
                if (isDesc) {
                    return valueB - valueA; // 降序：大到小
                } else {
                    return valueA - valueB; // 升序：小到大
                }
            });
            
            if (filteredData.length === 0) {
                showNoData('没有符合筛选条件的数据');
                return;
            }
            
            // 桌面版表格HTML
            const tableHtml = `
                <div class="table-header">
                    <div class="col">排名</div>
                    <div class="col">球员</div>
                    <div class="col">总场次</div>
                    <div class="col">胜场</div>
                    <div class="col">平局</div>
                    <div class="col">负场</div>
                    <div class="col">胜率</div>
                    <div class="col">类型</div>
                </div>
                ${filteredData.map((player, index) => {
                    const winRate = parseFloat(player.win_rate);
                    let winRateClass = 'winrate-low';
                    if (winRate >= 60) winRateClass = 'winrate-high';
                    else if (winRate >= 40) winRateClass = 'winrate-medium';
                    
                    return `
                        <div class="player-row">
                            <div class="rank">${index + 1}</div>
                            <div class="player-name">${player.player_name}</div>
                            <div>${player.total_matches}</div>
                            <div>${player.wins}</div>
                            <div>${player.draws}</div>
                            <div>${player.losses}</div>
                            <div class="winrate-value ${winRateClass}">${player.win_rate}%</div>
                            <div><span class="player-type ${player.player_type}">${player.player_type === 'regular' ? '常规' : '外援'}</span></div>
                        </div>
                    `;
                }).join('')}
            `;
            
            // 移动端卡片HTML
            const mobileCardsHtml = filteredData.map((player, index) => {
                const winRate = parseFloat(player.win_rate);
                let winRateClass = 'winrate-low';
                if (winRate >= 60) winRateClass = 'winrate-high';
                else if (winRate >= 40) winRateClass = 'winrate-medium';
                
                return `
                    <div class="player-card">
                        <div class="player-card-header">
                            <div class="player-card-rank">${index + 1}</div>
                            <div class="player-card-name">${player.player_name}</div>
                            <div class="player-card-type ${player.player_type}">${player.player_type === 'regular' ? '常规' : '外援'}</div>
                        </div>
                        
                        <div class="player-card-stats">
                            <div class="stat-item highlight">
                                <div class="stat-label">胜率</div>
                                <div class="stat-value winrate ${winRateClass}">${player.win_rate}%</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">总场次</div>
                                <div class="stat-value">${player.total_matches}</div>
                            </div>
                        </div>
                        
                        <div class="player-card-details">
                            <div class="detail-item">
                                <div class="detail-label">胜场</div>
                                <div class="detail-value">${player.wins}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">平局</div>
                                <div class="detail-value">${player.draws}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">负场</div>
                                <div class="detail-value">${player.losses}</div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('winrate-table-container').innerHTML = tableHtml;
            document.getElementById('mobile-cards-container').innerHTML = mobileCardsHtml;
        }
        
        // 显示无数据状态
        function showNoData(message = '暂无胜率数据') {
            document.getElementById('winrate-stats').innerHTML = '';
            document.getElementById('winrate-table-container').innerHTML = `<div class="no-data">${message}</div>`;
            document.getElementById('mobile-cards-container').innerHTML = '';
        }
        
        // 汉堡菜单控制函数
        function toggleMenu() {
            const navMenu = document.querySelector('.nav-menu');
            const hamburger = document.querySelector('.hamburger');
            
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }
        
        // 点击菜单项或页面其他区域时关闭菜单
        document.addEventListener('DOMContentLoaded', function() {
            // 点击菜单项时关闭菜单
            document.querySelectorAll('.nav-menu a').forEach(link => {
                link.addEventListener('click', () => {
                    document.querySelector('.nav-menu').classList.remove('active');
                    document.querySelector('.hamburger').classList.remove('active');
                });
            });
            
            // 点击页面其他区域时关闭菜单
            document.addEventListener('click', function(e) {
                const navMenu = document.querySelector('.nav-menu');
                const hamburger = document.querySelector('.hamburger');
                
                if (!navMenu.contains(e.target) && !hamburger.contains(e.target)) {
                    navMenu.classList.remove('active');
                    hamburger.classList.remove('active');
                }
            });
        });
    </script>
</body>
</html>