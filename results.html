<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>比赛结果 - 足球队数据统计系统</title>
    <link rel="stylesheet" href="styles.css?v=2.1">
    <style>
    /* 比赛卡片样式 */
    .match-results-section {
        padding: 2rem 0;
    }

    .match-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .match-card.pending {
        border-left: 4px solid #ffc107;
    }

    .match-card.completed {
        border-left: 4px solid #4caf50;
    }

    /* 比赛头部信息样式 */
    .match-header {
        background-color: #f8f9fa;
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .match-info {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .match-info span {
        color: #666;
    }

    .match-status {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: bold;
    }

    .match-status.pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .match-status.completed {
        background-color: #d4edda;
        color: #155724;
    }

    /* 比赛内容样式 */
    .match-content {
        padding: 1.5rem;
    }

    .team-lists {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin: 1rem 0;
    }

    .team h4 {
        color: #1E88E5;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #1E88E5;
    }

    .team ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .team li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
    }

    /* 比分显示样式 */
    .match-score {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2rem;
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .score {
        font-weight: bold;
        color: #1E88E5;
    }

    .score-separator {
        color: #666;
    }

    /* 统计数据样式 */
    .match-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .team-stats {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
    }

    .stats-section {
        margin-bottom: 1.5rem;
    }

    .stats-section h5 {
        color: #666;
        margin-bottom: 0.5rem;
    }

    .stats-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .stats-list li {
        padding: 0.3rem 0;
        border-bottom: 1px solid #eee;
    }

    .no-data {
        text-align: center;
        padding: 2rem;
        color: #666;
        font-size: 1.2rem;
    }

    /* 在 style 标签中添加以下样式 */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1000;
    }

    .modal-content.large-modal {
        background-color: white;
        margin: 5vh auto;
        padding: 2rem;
        width: 90%;
        max-width: 900px;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        max-height: 90vh;
        overflow-y: auto;
    }

    .edit-score-section {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 2rem;
    }

    .team-edit {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }

    .stats-category {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .add-stats-btn {
        width: 100%;
        padding: 0.8rem;
        background-color: #f5f5f5;
        color: #666;
        border: 1px dashed #ddd;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .add-stats-btn:hover {
        background-color: #e3f2fd;
        border-color: #1E88E5;
        color: #1E88E5;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        margin-left: 0.5rem;
    }

    .edit-btn {
        background-color: #1E88E5;
        color: white;
    }

    .delete-btn {
        background-color: #dc3545;
        color: white;
    }

    .match-actions {
        display: flex;
        justify-content: flex-end;
        padding-top: 1rem;
        border-top: 1px solid #eee;
        margin-top: 1rem;
    }

    /* 在已有的样式中添加 */
    .stats-input-group {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        align-items: center;
    }

    .player-select {
        flex: 2;
        padding: 0.3rem;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .stats-count {
        flex: 1;
        padding: 0.3rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        width: 60px;
    }

    .remove-stats-btn {
        padding: 0.3rem 0.6rem;
        background-color: #dc3545;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .remove-stats-btn:hover {
        background-color: #c82333;
    }

    /* 错误消息样式 */
    .error-message {
        padding: 1rem;
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeeba;
        border-radius: 4px;
        margin: 1rem 0;
        text-align: center;
    }

    /* 无数据提示样式 */
    .no-data {
        padding: 2rem;
        text-align: center;
        color: #666;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin: 1rem 0;
    }

    /* 比赛卡片样式 */
    .match-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        padding: 1.5rem;
    }

    .match-info {
        margin-bottom: 1.5rem;
    }

    .match-date {
        font-size: 1.2rem;
        font-weight: bold;
        color: white;
        margin-bottom: 0.5rem;
    }

    .match-details {
        color: #666;
    }

    .match-details span:not(:last-child)::after {
        content: " | ";
        color: #ddd;
        margin: 0 0.5rem;
    }

    /* 比赛卡片样式优化 */
    .match-card {
        background: linear-gradient(to bottom, #ffffff, #f8f9fa);
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .match-header {
        background: linear-gradient(135deg, #1E88E5, #1565C0);
        color: white;
        padding: 1.5rem;
    }

    .match-date {
        font-size: 1.4rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .match-meta {
        display: flex;
        gap: 1.5rem;
        font-size: 1rem;
    }

    .match-meta span {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .match-score-section {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 2rem;
        background-color: white;
        gap: 3rem;
    }

    .team {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .team-name {
        font-size: 1.2rem;
        font-weight: bold;
        color: #333;
    }

    .score {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1E88E5;
    }

    .score-separator {
        font-size: 1.5rem;
        color: #666;
        font-weight: bold;
    }

    /* 球员名单样式 */
    .match-players-section {
        padding: 2rem;
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .team-players h4 {
        color: #1E88E5;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #1E88E5;
    }

    .players-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .player-tag {
        background-color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        color: #333;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 统计数据样式优化 */
    .match-stats-section {
        padding: 2rem;
        background-color: white;
    }

    .section-title {
        text-align: center;
        color: #1E88E5;
        margin-bottom: 1.5rem;
        font-size: 1.4rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .team-stats {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .team-stats h4 {
        color: #1E88E5;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #1E88E5;
    }

    .stats-category {
        background-color: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .stats-category h5 {
        color: #666;
        margin-bottom: 0.8rem;
        font-size: 1rem;
    }

    .stats-category ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .stats-category li {
        padding: 0.3rem 0.5rem;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        line-height: 1.2;
    }

    .stats-category li:last-child {
        border-bottom: none;
    }

    /* 球员统计数据优化显示 */
    .player-stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .player-name {
        font-weight: 500;
        color: #333;
        flex: 1;
        text-align: left;
    }

    .stat-count {
        font-weight: bold;
        color: #1E88E5;
        font-size: 0.85rem;
        min-width: 40px;
        text-align: right;
    }

    /* 移动端响应式样式 */
    @media (max-width: 768px) {
        .match-card {
            margin-bottom: 1.5rem;
            border-radius: 8px;
        }
        
        .match-header {
            padding: 1rem;
        }
        
        .match-date {
            font-size: 1.2rem;
        }
        
        .match-meta {
            flex-direction: column;
            gap: 0.5rem;
            font-size: 0.9rem;
        }
        
        .match-score-section {
            padding: 1.5rem 1rem;
            gap: 2rem;
        }
        
        .score {
            font-size: 2rem;
        }
        
        .match-players-section {
            grid-template-columns: 1fr;
            gap: 1.5rem;
            padding: 1.5rem 1rem;
        }
        
        .team-players h4 {
            font-size: 1.1rem;
        }
        
        .player-tag {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
        }
        
        .match-stats-section {
            padding: 1.5rem 1rem;
        }
        
        .section-title {
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        .team-stats {
            padding: 1rem;
        }
        
        .team-stats h4 {
            font-size: 1.1rem;
        }
        
        .stats-category {
            padding: 0.8rem;
            margin-bottom: 0.8rem;
        }
        
        .stats-category h5 {
            font-size: 0.9rem;
            margin-bottom: 0.6rem;
        }
        
        .stats-category li {
            padding: 0.4rem 0.3rem;
            font-size: 0.85rem;
        }
        
        .match-actions {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .action-btn {
            width: 100%;
            padding: 0.8rem;
            font-size: 1rem;
        }
        
        /* 编辑弹窗移动端优化 */
        .modal-content.large-modal {
            margin: 2vh auto;
            width: 95%;
            max-height: 96vh;
            padding: 1rem;
        }
        
        .edit-score-section {
            padding: 1rem;
        }
        
        .team-edit {
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .stats-input-group {
            flex-direction: column;
            gap: 0.3rem;
            align-items: stretch;
        }
        
        .player-select,
        .stats-count {
            width: 100%;
            padding: 0.5rem;
        }
        
        .remove-stats-btn {
            align-self: flex-end;
            width: auto;
            padding: 0.4rem 0.8rem;
        }
    }
    </style>

    <!-- 添加 Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-brand">丛莱梅蔬果足球队</div>
        <ul class="nav-menu">
            <li><a href="index.html">首页</a></li>
            <li><a href="results.html" class="active">比赛结果</a></li>
            <li><a href="stats.html">球员统计</a></li>
            <li><a href="winrate.html">胜率统计</a></li>
        </ul>
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="container">
        <section class="match-results-section">
            <h2>比赛结果</h2>
            <div class="match-results-container">
                <!-- 比赛记录将通过 JavaScript 动态生成 -->
            </div>
        </section>
    </main>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <a href="index.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-home"></i></div>
            <div class="mobile-nav-text">首页</div>
        </a>
        <a href="results.html" class="mobile-nav-item active">
            <div class="mobile-nav-icon"><i class="fas fa-trophy"></i></div>
            <div class="mobile-nav-text">结果</div>
        </a>
        <a href="stats.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-chart-bar"></i></div>
            <div class="mobile-nav-text">统计</div>
        </a>
        <a href="winrate.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-percentage"></i></div>
            <div class="mobile-nav-text">胜率</div>
        </a>
    </nav>

    <!-- 页脚 -->
    <footer class="footer">
        <p>© 2013-2025 丛莱梅蔬果足球队</p>
    </footer>

    <!-- 编辑弹窗 -->
    <div id="editModal" class="modal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3>编辑比赛数据</h3>
                <button class="close-btn" onclick="closeEditModal()">×</button>
            </div>
            <div class="modal-body">
                <!-- 比分编辑 -->
                <div class="edit-score-section">
                    <h4>比分</h4>
                    <div class="score-input">
                        <div class="team-score">
                            <span class="team-name">白队</span>
                            <input type="number" id="editWhiteScore" min="0" value="0">
                        </div>
                        <span class="score-separator">:</span>
                        <div class="team-score">
                            <input type="number" id="editBlueScore" min="0" value="0">
                            <span class="team-name">蓝队</span>
                        </div>
                    </div>
                </div>
                <!-- 统计数据编辑 -->
                <div class="edit-stats-section">
                    <div class="team-edit">
                        <h4>白队数据</h4>
                        <div class="stats-category">
                            <h5>进球</h5>
                            <div id="editWhiteStatsGoals"></div>
                            <button class="add-stats-btn" onclick="addStats('white', 'goal')">
                                <span class="btn-icon">+</span> 添加进球
                            </button>
                        </div>
                        <div class="stats-category">
                            <h5>助攻</h5>
                            <div id="editWhiteStatsAssists"></div>
                            <button class="add-stats-btn" onclick="addStats('white', 'assist')">
                                <span class="btn-icon">+</span> 添加助攻
                            </button>
                        </div>
                    </div>
                    <div class="team-edit">
                        <h4>蓝队数据</h4>
                        <div class="stats-category">
                            <h5>进球</h5>
                            <div id="editBlueStatsGoals"></div>
                            <button class="add-stats-btn" onclick="addStats('blue', 'goal')">
                                <span class="btn-icon">+</span> 添加进球
                            </button>
                        </div>
                        <div class="stats-category">
                            <h5>助攻</h5>
                            <div id="editBlueStatsAssists"></div>
                            <button class="add-stats-btn" onclick="addStats('blue', 'assist')">
                                <span class="btn-icon">+</span> 添加助攻
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeEditModal()">取消</button>
                <button class="btn-primary" onclick="saveEdit()">保存</button>
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div id="deleteConfirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="close-btn" onclick="closeDeleteConfirmModal()">×</button>
            </div>
            <div class="modal-body">
                <p>确定要删除这场比赛记录吗？此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeDeleteConfirmModal()">取消</button>
                <button class="btn-primary" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 在 <script> 标签之前添加 Supabase 客户端库 -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/dist/umd/supabase.min.js"></script>

    <script>
    // 初始化 Supabase 客户端
    const { createClient } = supabase;
    const supabaseClient = createClient(
        'https://obidukxlcgecpooynqnz.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9iaWR1a3hsY2dlY3Bvb3lucW56Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQyNjAyMDgsImV4cCI6MjA0OTgzNjIwOH0.a4Y-nWWxb8ClbMO2BUXG2vTJMqTJe2rQAXdWyKHZlHs'
    );

    // 页面加载时获取并显示比赛数据
    document.addEventListener('DOMContentLoaded', async function() {
        try {
            // 获取所有已完成的比赛
            const { data: matches, error: matchError } = await supabaseClient
                .from('matches')
                .select('*')
                .eq('status', 'completed')
                .order('match_date', { ascending: false });

            if (matchError) throw matchError;

            // 获取所有比赛的统计数据
            const { data: allStats, error: statsError } = await supabaseClient
                .from('match_stats')
                .select('*')
                .in('match_id', matches.map(m => m.id));

            if (statsError) throw statsError;

            // 获取所有比赛的球员数据
            const { data: allPlayers, error: playersError } = await supabaseClient
                .from('match_players')
                .select('*')
                .in('match_id', matches.map(m => m.id));

            if (playersError) throw playersError;

            // 显示每场比赛的数据
            const container = document.querySelector('.match-results-container');
            if (!container) throw new Error('找不到比赛结果容器元素');

            if (!matches?.length) {
                container.innerHTML = '<div class="no-data">暂无比赛记录</div>';
                return;
            }

            // 筛选出 2025 年的比赛并按日期升序排序
            const matches2025 = matches
                .filter(match => new Date(match.match_date).getFullYear() === 2025)
                .sort((a, b) => new Date(a.match_date) - new Date(b.match_date));

            container.innerHTML = matches.map(match => {
                // 获取这场比赛的数据
                const matchStats = allStats.filter(stat => stat.match_id === match.id);
                const matchPlayers = allPlayers.filter(player => player.match_id === match.id);
                
                // 分离白队和蓝队数据
                const whiteTeamStats = matchStats.filter(stat => stat.team === 'white');
                const blueTeamStats = matchStats.filter(stat => stat.team === 'blue');
                const whitePlayers = matchPlayers.filter(player => player.team === 'white');
                const bluePlayers = matchPlayers.filter(player => player.team === 'blue');

                // 获取 2025 年比赛的序号
                const match2025Index = matches2025.findIndex(m => m.id === match.id);
                const matchNumberLabel = match2025Index !== -1 ? `2025年第 ${match2025Index + 1} 场` : '';

                return `
                     <div class="match-header">
                            <div class="match-date">${formatDate(match.match_date)}</div>
                            <div class="match-meta">
                                <span class="match-number">${matchNumberLabel}</span>
                                <span class="match-time"><i class="fas fa-clock"></i> ${match.match_time}</span>
                                <span class="match-type"><i class="fas fa-futbol"></i> ${match.match_type}</span>
                                ${match.opponent_team ? `<span class="match-opponent"><i class="fas fa-users"></i> VS ${match.opponent_team}</span>` : ''}
                                <span class="match-venue"><i class="fas fa-map-marker-alt"></i> ${match.venue}</span>
                            </div>
                        </div>
                        
                        <div class="match-score-section">
                            <div class="team white-team">
                                <span class="team-name">白队</span>
                                <span class="score">${match.white_score || 0}</span>
                            </div>
                            <div class="score-separator">VS</div>
                            <div class="team blue-team">
                                <span class="score">${match.blue_score || 0}</span>
                                <span class="team-name">蓝队</span>
                            </div>
                        </div>

                        <div class="match-players-section">
                            <div class="team-players white">
                                <h4>白队阵容</h4>
                                <div class="players-list">
                                    ${whitePlayers.map(p => `<span class="player-tag">${p.player_name}</span>`).join('')}
                                </div>
                            </div>
                            <div class="team-players blue">
                                <h4>蓝队阵容</h4>
                                <div class="players-list">
                                    ${bluePlayers.map(p => `<span class="player-tag">${p.player_name}</span>`).join('')}
                                </div>
                            </div>
                        </div>

                        <div class="match-stats-section">
                            <h3 class="section-title">数据统计</h3>
                            <div class="stats-grid">
                                <div class="team-stats white">
                                    <h4>白队数据</h4>
                                    ${generateTeamStatsHTML(whiteTeamStats)}
                                </div>
                                <div class="team-stats blue">
                                    <h4>蓝队数据</h4>
                                    ${generateTeamStatsHTML(blueTeamStats)}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

        } catch (error) {
            console.error('获取比赛数据失败:', error);
            const container = document.querySelector('.match-results-container');
            if (container) {
                container.innerHTML = `
                    <div class="error-message">
                        获取比赛数据失败：${error.message}
                    </div>
                `;
            }
        }
    });

    // 生成队伍统计数据的 HTML
    function generateTeamStatsHTML(teamStats) {
        if (!teamStats || teamStats.length === 0) {
            return '<p>无数据</p>';
        }

        // 按进球数排序
        const sortedByGoals = [...teamStats]
            .filter(stat => stat.goals > 0)
            .sort((a, b) => b.goals - a.goals);

        // 按助攻数排序
        const sortedByAssists = [...teamStats]
            .filter(stat => stat.assists > 0)
            .sort((a, b) => b.assists - a.assists);

        return `
            <div class="stats-category">
                <h5>进球</h5>
                <ul>
                    ${sortedByGoals.length > 0 
                        ? sortedByGoals.map(stat => 
                            `<li><div class="player-stat-item"><span class="player-name">${stat.player_name}</span><span class="stat-count">${stat.goals}球</span></div></li>`
                        ).join('')
                        : '<li>无</li>'
                    }
                </ul>
            </div>
            <div class="stats-category">
                <h5>助攻</h5>
                <ul>
                    ${sortedByAssists.length > 0 
                        ? sortedByAssists.map(stat => 
                            `<li><div class="player-stat-item"><span class="player-name">${stat.player_name}</span><span class="stat-count">${stat.assists}次</span></div></li>`
                        ).join('')
                        : '<li>无</li>'
                    }
                </ul>
            </div>
        `;
    }

    // 格式化日期
    function formatDate(dateString) {
        const date = new Date(dateString);
        return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    }

    let currentEditIndex = -1;
    let currentDeleteIndex = -1;

    // 显示编辑弹窗
    function showEditModal(matchId) {
        const matchHistory = JSON.parse(localStorage.getItem('matchHistory') || '[]');
        const matchIndex = matchHistory.findIndex(match => match.id === matchId);
        if (matchIndex === -1) return;
        
        currentEditIndex = matchIndex;
        const match = matchHistory[matchIndex];
        
        // 设置比分
        document.getElementById('editWhiteScore').value = match.teams.white.score;
        document.getElementById('editBlueScore').value = match.teams.blue.score;
        
        // 显示现有数据
        displayEditStats('editWhiteStats', match.teams.white, match.teams.white.players);
        displayEditStats('editBlueStats', match.teams.blue, match.teams.blue.players);
        
        document.getElementById('editModal').style.display = 'block';
    }

    // 关闭编辑弹窗
    function closeEditModal() {
        document.getElementById('editModal').style.display = 'none';
        currentEditIndex = -1;
    }

    // 显示删除确认弹窗
    async function showDeleteConfirm(matchId) {
        try {
            // 获取比赛信息
            const { data: match, error } = await supabaseClient
                .from('matches')
                .select('*')
                .eq('id', matchId)
                .single();
            
            if (error) throw error;
            
            if (!match) {
                console.error('未找到要删除的记录');
                return;
            }
            
            // 显示确认弹窗
            const confirmMessage = document.querySelector('#deleteConfirmModal .modal-body p');
            const date = new Date(match.match_date);
            confirmMessage.textContent = `确定要删除 ${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 的比赛记录吗？此操作不可恢复。`;
            
            // 保存要删除的比赛ID
            document.getElementById('deleteConfirmModal').dataset.matchId = matchId;
            
            // 显示弹窗
            document.getElementById('deleteConfirmModal').style.display = 'block';
        } catch (error) {
            console.error('获取比赛信息失败:', error);
            alert('获取比赛信息失败：' + error.message);
        }
    }

    // 确认删除
    async function confirmDelete() {
        try {
            const modal = document.getElementById('deleteConfirmModal');
            const matchId = modal.dataset.matchId;
            
            if (!matchId) {
                throw new Error('未找到要删除的记录ID');
            }

            // 删除比赛记录（关联的球员记录会自动删除）
            const { error: matchError } = await supabaseClient
                .from('matches')
                .delete()
                .eq('id', matchId);

            if (matchError) throw matchError;

            // 关闭删除确认弹窗
            closeDeleteConfirmModal();
            
            // 刷新页面显示
            window.location.reload();
        } catch (error) {
            console.error('删除失败:', error);
            alert('删除失败：' + error.message);
        }
    }

    // 关闭删除确认弹窗
    function closeDeleteConfirmModal() {
        document.getElementById('deleteConfirmModal').style.display = 'none';
        currentDeleteIndex = -1;
    }

    // 点击弹窗外部关闭
    window.onclick = function(event) {
        const editModal = document.getElementById('editModal');
        const deleteModal = document.getElementById('deleteConfirmModal');
        
        if (event.target === editModal) {
            closeEditModal();
        } else if (event.target === deleteModal) {
            closeDeleteConfirmModal();
        }
    }

    // 显示编辑区域的统计数据
    function displayEditStats(containerId, teamData, players) {
        // 分别显示进球和助攻数据
        const goalsContainer = document.getElementById(`${containerId}Goals`);
        const assistsContainer = document.getElementById(`${containerId}Assists`);
        
        // 显示进球数据
        goalsContainer.innerHTML = teamData.stats.goals.map(goal => 
            createStatsInputGroup(players, goal.player, goal.count, 'goal')
        ).join('');
        
        // 显示助攻数据
        assistsContainer.innerHTML = teamData.stats.assists.map(assist => 
            createStatsInputGroup(players, assist.player, assist.count, 'assist')
        ).join('');
    }

    // 创建统计数据输入组
    function createStatsInputGroup(players, selectedPlayer, count, type) {
        const options = players.map(player => 
            `<option value="${player}" ${player === selectedPlayer ? 'selected' : ''}>${player}</option>`
        ).join('');
        
        return `
            <div class="stats-input-group">
                <select class="player-select">
                    ${options}
                </select>
                <input type="number" class="stats-count" value="${count}" min="0">
                <input type="hidden" class="stats-type" value="${type}">
                <button class="remove-stats-btn" onclick="removeStatsGroup(this)">×</button>
            </div>
        `;
    }

    // 添加新的统计数据
    function addStats(team, type) {
        const matchHistory = JSON.parse(localStorage.getItem('matchHistory') || '[]');
        const match = matchHistory[currentEditIndex];
        const players = match.teams[team].players;
        const container = document.getElementById(`edit${team.charAt(0).toUpperCase() + team.slice(1)}${type === 'goal' ? 'Goals' : 'Assists'}`);
        
        container.insertAdjacentHTML('beforeend', createStatsInputGroup(players, players[0], 0, type));
    }

    // 移除统计数据组
    function removeStatsGroup(button) {
        button.closest('.stats-input-group').remove();
    }

    // 收集编辑区域的统计数据
    function collectEditStats(teamPrefix) {
        const stats = {
            goals: [],
            assists: []
        };
        
        // 收集进球数据
        document.querySelectorAll(`#edit${teamPrefix}Goals .stats-input-group`).forEach(group => {
            const player = group.querySelector('.player-select').value;
            const count = parseInt(group.querySelector('.stats-count').value) || 0;
            if (count > 0) {
                stats.goals.push({ player: player, count: count });
            }
        });
        
        // 收集助攻数据
        document.querySelectorAll(`#edit${teamPrefix}Assists .stats-input-group`).forEach(group => {
            const player = group.querySelector('.player-select').value;
            const count = parseInt(group.querySelector('.stats-count').value) || 0;
            if (count > 0) {
                stats.assists.push({ player: player, count: count });
            }
        });
        
        return stats;
    }

    // 保存编辑
    function saveEdit() {
        if (!confirm('确认保存修改？')) {
            return;
        }
        
        const matchHistory = JSON.parse(localStorage.getItem('matchHistory') || '[]');
        const match = matchHistory[currentEditIndex];
        
        // 更新比分
        match.teams.white.score = parseInt(document.getElementById('editWhiteScore').value) || 0;
        match.teams.blue.score = parseInt(document.getElementById('editBlueScore').value) || 0;
        
        // 更新统计数据
        match.teams.white.stats = collectEditStats('WhiteStats');
        match.teams.blue.stats = collectEditStats('BlueStats');
        
        // 保存更新
        localStorage.setItem('matchHistory', JSON.stringify(matchHistory));
        
        // 关闭弹窗并刷新显示
        closeEditModal();
        window.location.reload();
    }

    // 汉堡菜单切换
    function toggleMenu() {
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    }

    // 点击菜单项时关闭菜单
    document.addEventListener('DOMContentLoaded', function() {
        const navLinks = document.querySelectorAll('.nav-menu a');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                const hamburger = document.querySelector('.hamburger');
                const navMenu = document.querySelector('.nav-menu');
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    });

    // 点击页面其他地方时关闭菜单
    document.addEventListener('click', function(event) {
        const navbar = document.querySelector('.navbar');
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        if (!navbar.contains(event.target) && navMenu.classList.contains('active')) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }
    });
    </script>
</body>
</html>