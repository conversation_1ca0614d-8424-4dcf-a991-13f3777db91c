<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>比赛记录 - 足球队数据统计系统</title>
    <link rel="stylesheet" href="styles.css?v=2.1">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/dist/umd/supabase.min.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-brand">⚽ 足球队管理系统</div>
        <ul class="nav-menu">
            <li><a href="index.html">首页</a></li>
            <li><a href="results.html">比赛结果</a></li>
            <li><a href="stats.html">球员统计</a></li>
            <li><a href="winrate.html">胜率统计</a></li>
        </ul>
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="container">
        <!-- 比赛信息展示 -->
        <section class="match-info-display">
            <h2>本场比赛信息</h2>
            <div class="match-info-card">
                <div class="match-info-row">
                    <span class="info-item"><strong>比赛日期：</strong><span id="displayMatchDate"></span></span>
                    <span class="info-item"><strong>比赛时间：</strong><span id="displayMatchTime"></span></span>
                    <span class="info-item"><strong>比赛性质：</strong><span id="displayMatchType"></span></span>
                    <span class="info-item"><strong>比赛场地：</strong><span id="displayMatchVenue"></span></span>
                </div>
            </div>
        </section>

        <!-- 队伍名单展示 -->
        <section class="teams-display">
            <div class="teams-grid">
                <div class="team-column">
                    <h3>白队名单</h3>
                    <ul id="whiteTeamList" class="team-list">
                        <!-- 这里的列表项会通过 JavaScript 动态生成 -->
                    </ul>
                    <!-- 白队统计结果 -->
                    <div id="whiteFinalStats" class="final-stats-display" style="display: none;">
                        <h4>数据统计</h4>
                        <div class="stats-content"></div>
                    </div>
                </div>
                <div class="team-column">
                    <h3>蓝队名单</h3>
                    <ul id="blueTeamList" class="team-list">
                        <!-- 这里的列表项会通过 JavaScript 动态生成 -->
                    </ul>
                    <!-- 蓝队统计结果 -->
                    <div id="blueFinalStats" class="final-stats-display" style="display: none;">
                        <h4>数据统计</h4>
                        <div class="stats-content"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 比赛结果记录 -->
        <section class="match-result-section">
            <h2>比赛结果</h2>
            <div class="match-result-card">
                <div class="score-input">
                    <div class="team-score">
                        <span class="team-name">白队</span>
                        <input type="number" id="whiteTeamScore" min="0" value="0">
                    </div>
                    <span class="score-separator">:</span>
                    <div class="team-score">
                        <input type="number" id="blueTeamScore" min="0" value="0">
                        <span class="team-name">蓝队</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 数据记录区域 -->
        <section class="stats-record">
            <h2>比赛数据记录</h2>
            <div class="stats-grid">
                <div class="team-stats">
                    <h3>白队数据</h3>
                    <div id="whiteTeamStats" class="player-stats-list"></div>
                </div>
                <div class="team-stats">
                    <h3>蓝队数据</h3>
                    <div id="blueTeamStats" class="player-stats-list"></div>
                </div>
            </div>
            <!-- 确认统计按钮 -->
            <div class="confirm-stats-section">
                <button class="confirm-stats-btn" onclick="showFinalStats()">确认数据统计</button>
            </div>
        </section>

        <!-- 最终统计结果弹窗 -->
        <div id="finalStatsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>本场比赛数据统计</h3>
                    <button class="close-btn" onclick="closeFinalStatsModal()">×</button>
                </div>
                <div class="modal-body">
                    <!-- 比分展示 -->
                    <div class="final-score-display">
                        <h4>比赛结果</h4>
                        <div class="final-score">
                            <span class="team-name">白队</span>
                            <span id="finalWhiteScore">0</span>
                            <span class="score-separator">:</span>
                            <span id="finalBlueScore">0</span>
                            <span class="team-name">蓝队</span>
                        </div>
                    </div>
                    <div class="final-stats-grid">
                        <div class="final-team-stats">
                            <h4>白队数据统计</h4>
                            <div class="stats-category">
                                <h5>进球</h5>
                                <ul id="whiteTeamGoals" class="stats-list"></ul>
                            </div>
                            <div class="stats-category">
                                <h5>助攻</h5>
                                <ul id="whiteTeamAssists" class="stats-list"></ul>
                            </div>
                        </div>
                        <div class="final-team-stats">
                            <h4>蓝队数据统计</h4>
                            <div class="stats-category">
                                <h5>进球</h5>
                                <ul id="blueTeamGoals" class="stats-list"></ul>
                            </div>
                            <div class="stats-category">
                                <h5>助攻</h5>
                                <ul id="blueTeamAssists" class="stats-list"></ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="confirm-btn" onclick="saveFinalStats()">确认</button>
                </div>
            </div>
        </div>
    </main>

    <!-- 数据输入弹窗 -->
    <div id="statsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>记录数据</h3>
                <button class="close-btn" onclick="closeModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="input-group">
                    <label id="modalLabel"></label>
                    <input type="number" id="statsInput" min="0" value="0">
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="saveStats()" class="confirm-btn">确认</button>
            </div>
        </div>
    </div>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <a href="index.html" class="mobile-nav-item active">
            <div class="mobile-nav-icon"><i class="fas fa-home"></i></div>
            <div class="mobile-nav-text">首页</div>
        </a>
        <a href="results.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-trophy"></i></div>
            <div class="mobile-nav-text">结果</div>
        </a>
        <a href="stats.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-chart-bar"></i></div>
            <div class="mobile-nav-text">统计</div>
        </a>
        <a href="winrate.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-percentage"></i></div>
            <div class="mobile-nav-text">胜率</div>
        </a>
    </nav>

    <!-- 页脚 -->
    <footer class="footer">
        <p>© 2024 足球队报名与数据统计系统</p>
    </footer>

    <style>
    /* 比赛信息展示样式 */
    .match-info-display {
        margin-bottom: 2rem;
    }

    .match-info-card {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .match-info-row {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1.5rem;
    }

    .info-item {
        flex: 1;
        min-width: 200px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-item strong {
        color: #1E88E5;
        white-space: nowrap;
    }

    /* 队伍展示样式 */
    .teams-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .team-column {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .team-column h3 {
        color: #1E88E5;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #1E88E5;
    }

    .team-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .team-list li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .delete-player {
        background: none;
        border: none;
        color: #dc3545;
        cursor: pointer;
        font-size: 1.2rem;
        padding: 0 0.5rem;
        transition: color 0.3s;
    }

    .delete-player:hover {
        color: #c82333;
    }

    /* 数据记录样式 */
    .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .team-stats {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .team-stats h3 {
        color: #1E88E5;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #1E88E5;
    }

    .player-stats-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .player-stats-item {
        display: flex;
        align-items: center;
        padding: 0.8rem;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .player-name {
        flex: 1;
        font-weight: bold;
        margin-right: 1rem;
    }

    .stats-buttons {
        display: flex;
        gap: 1rem;
    }

    .stats-control {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .stats-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s;
        width: 100%;
        text-align: center;
    }

    .goal-btn {
        background-color: #4CAF50;
        color: white;
    }

    .assist-btn {
        background-color: #2196F3;
        color: white;
    }

    .number-control {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background-color: #f8f9fa;
        padding: 0.3rem;
        border-radius: 4px;
    }

    .control-btn {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        border-radius: 4px;
        background-color: #e9ecef;
        color: #495057;
        cursor: pointer;
        font-size: 1rem;
        transition: all 0.2s;
    }

    .control-btn:hover {
        background-color: #dee2e6;
    }

    .control-btn:active {
        background-color: #ced4da;
    }

    .stats-count {
        min-width: 20px;
        text-align: center;
        font-weight: bold;
        color: #495057;
    }

    /* 弹窗样式 */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1000;
        overflow-y: auto;
    }

    .modal-content {
        position: relative;
        background-color: white;
        margin: 5vh auto;
        padding: 2rem;
        width: 90%;
        max-width: 800px;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;
    }

    .input-group {
        margin-bottom: 1.5rem;
    }

    .input-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: bold;
    }

    .input-group input {
        width: 100%;
        padding: 0.8rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
    }

    .modal-footer {
        position: sticky;
        bottom: 0;
        background-color: white;
        padding: 1rem 0;
        border-top: 1px solid #eee;
        margin-top: 1rem;
    }

    .confirm-btn {
        padding: 0.8rem 1.5rem;
        background-color: #1E88E5;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1rem;
    }

    .confirm-btn:hover {
        background-color: #1976D2;
    }

    /* 确认数据统计按钮样式 */
    .confirm-stats-section {
        text-align: center;
        margin-top: 2rem;
    }

    .confirm-stats-btn {
        padding: 1rem 2rem;
        background-color: #43A047;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1.1rem;
        transition: all 0.3s;
    }

    .confirm-stats-btn:hover {
        background-color: #388E3C;
    }

    /* 最终统计结果样式 */
    .final-stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .final-team-stats {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
    }

    .final-team-stats h4 {
        color: #1E88E5;
        margin: 0 0 1rem 0;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #1E88E5;
    }

    .stats-category {
        margin-bottom: 1.5rem;
    }

    .stats-category h5 {
        color: #666;
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
    }

    .stats-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .stats-list li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
        color: #333;
    }

    .stats-list li:last-child {
        border-bottom: none;
    }

    /* 比赛结果样式 */
    .match-result-section {
        margin: 2rem 0;
    }

    .match-result-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .score-input {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
    }

    .team-score {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .team-score input {
        width: 60px;
        padding: 0.5rem;
        font-size: 1.5rem;
        text-align: center;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .score-separator {
        font-size: 2rem;
        font-weight: bold;
        color: #666;
    }

    .team-name {
        font-size: 1.2rem;
        font-weight: bold;
        color: #1E88E5;
    }

    /* 最终比分展示样式 */
    .final-score-display {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
    }

    .final-score-display h4 {
        color: #1E88E5;
        margin: 0 0 1rem 0;
    }

    .final-score {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        font-size: 1.5rem;
    }

    .final-score .score-separator {
        margin: 0 1rem;
    }

    /* 统计结果展示样式 */
    .final-stats-display {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 2px solid #1E88E5;
    }

    .final-stats-display h4 {
        color: #1E88E5;
        margin: 0 0 1rem 0;
    }

    .stats-content {
        font-size: 0.9rem;
    }

    .stats-content .stats-category {
        margin-bottom: 1rem;
    }

    .stats-content .stats-category:last-child {
        margin-bottom: 0;
    }

    .stats-content h5 {
        color: #666;
        margin: 0 0 0.5rem 0;
    }

    .stats-content ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .stats-content li {
        padding: 0.3rem 0;
        border-bottom: 1px solid #eee;
    }

    .stats-content li:last-child {
        border-bottom: none;
    }

    /* 添加最终统计弹窗的特殊样式 */
    #finalStatsModal .modal-content {
        max-height: 90vh;
        overflow-y: auto;
        margin: 5vh auto;
    }
    </style>

    <script>
    let currentPlayer = null;
    let currentAction = null;

    // 初始化 Supabase 客户端
    const { createClient } = supabase;
    const supabaseClient = createClient(
        'https://obidukxlcgecpooynqnz.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9iaWR1a3hsY2dlY3Bvb3lucW56Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQyNjAyMDgsImV4cCI6MjA0OTgzNjIwOH0.a4Y-nWWxb8ClbMO2BUXG2vTJMqTJe2rQAXdWyKHZlHs'
    );

    // 添加通用的权限验证函数
    async function checkAdminAuth() {
        try {
            const { data: { session } } = await supabaseClient.auth.getSession();
            if (!session) {
                alert('请先登录管理员账号');
                window.location.href = 'index.html';
                return false;
            }
            return true;
        } catch (error) {
            console.error('权限检查失败:', error);
            alert('权限检查失败，请重新登录');
            window.location.href = 'index.html';
            return false;
        }
    }

    // 修改页面加载事件
    document.addEventListener('DOMContentLoaded', async function() {
        // 首先检查权限
        if (!await checkAdminAuth()) {
            return;
        }

        try {
            // 继续执行原有的页面加载代码...
            const matchDataStr = localStorage.getItem('currentMatch');
            const matchData = JSON.parse(matchDataStr);
            console.log('localStorage中的数据:', matchData);

            if (!matchData?.matchId) {
                throw new Error('本地没有找到比赛信息');
            }

            // 先用 localStorage 的数据渲染页面
            displayMatchInfo(matchData.matchInfo);
            displayTeamList('whiteTeamList', matchData.teams.white.players);
            displayTeamList('blueTeamList', matchData.teams.blue.players);
            initializeStatsSection('whiteTeamStats', matchData.teams.white.players);
            initializeStatsSection('blueTeamStats', matchData.teams.blue.players);

            // 然后从 Supabase 获取数据进行验证
            const { data: match, error: matchError } = await supabaseClient
                .from('matches')
                .select('*')
                .eq('id', matchData.matchId)
                .single();

            if (matchError) {
                console.error('验证比赛信息失败:', matchError);
                // 这里我们不抛出错误，因为本地数据可能还未同步到数据库
                return;
            }

            // 如果数据库中找不到数据，说明可能还未保存成功
            if (!match) {
                console.warn('数据库中未找到比赛数据，可能正在保存中');
                return;
            }

            // 如果数据库中有数据，则更新显示
            displayMatchInfo(match);

            // 获取最新的球员信息
            const { data: players, error: playersError } = await supabaseClient
                .from('match_players')
                .select('player_name, team')
                .eq('match_id', matchData.matchId);

            if (playersError) {
                console.error('获取球员信息失败:', playersError);
                return;
            }

            // 更新球员显示
            const whitePlayers = players
                .filter(p => p.team === 'white')
                .map(p => p.player_name);
            const bluePlayers = players
                .filter(p => p.team === 'blue')
                .map(p => p.player_name);

            displayTeamList('whiteTeamList', whitePlayers);
            displayTeamList('blueTeamList', bluePlayers);
            initializeStatsSection('whiteTeamStats', whitePlayers);
            initializeStatsSection('blueTeamStats', bluePlayers);

        } catch (error) {
            console.error('读取数据失��:', error);
            alert('读取数据失败，请返回报名页面重试');
            window.location.href = 'signup.html';
        }
    });

    // 显示比赛信息的辅助函数
    function displayMatchInfo(matchInfo) {
        document.getElementById('displayMatchDate').textContent = formatDate(matchInfo.match_date);
        document.getElementById('displayMatchTime').textContent = matchInfo.match_time;
        document.getElementById('displayMatchType').textContent = matchInfo.match_type;
        document.getElementById('displayMatchVenue').textContent = matchInfo.venue;
    }

    // 显示队伍名单
    function displayTeamList(elementId, players) {
        console.log('显示队伍名单');
        console.log('elementId:', elementId);
        console.log('players:', players);
        
        const list = document.getElementById(elementId);
        if (!list) {
            console.error('找不到元素:', elementId);
            return;
        }

        // 确保我们有正确的球员数组
        const playerArray = Array.isArray(players) ? players : 
                           (players?.players && Array.isArray(players.players)) ? players.players : 
                           [];
        
        const teamType = elementId === 'whiteTeamList' ? 'white' : 'blue';
        console.log('队伍类型:', teamType);
        console.log('处理后的球员数组:', playerArray);
        
        // 清空现有内容
        list.innerHTML = '';
        
        // 为每个球员创建列表项
        playerArray.forEach(player => {
            console.log('创建队员列表项:', player);
            
            const li = document.createElement('li');
            
            // 创建球员名字的 span 元素
            const nameSpan = document.createElement('span');
            nameSpan.textContent = player;
            nameSpan.className = 'player-name-span';
            
            // 创建删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-player';
            deleteBtn.textContent = '×';
            deleteBtn.onclick = () => removePlayer(player, teamType);
            
            // 组装列表项
            li.appendChild(nameSpan);
            li.appendChild(deleteBtn);
            list.appendChild(li);
        });
    }

    // 修改 initializeStatsSection 函数
    function initializeStatsSection(elementId, players) {
        console.log('初始化统计区域');
        console.log('elementId:', elementId);
        console.log('players:', players);

        // 确保我们有正确的球员数组
        const playerArray = Array.isArray(players) ? players : 
                           (players?.players && Array.isArray(players.players)) ? players.players : 
                           [];

        console.log('处理后的球员数组:', playerArray);

        const container = document.getElementById(elementId);
        container.innerHTML = playerArray.map(player => `
            <div class="player-stats-item">
                <span class="player-name">${player}</span>
                <div class="stats-buttons">
                    <div class="stats-control">
                        <button class="stats-btn goal-btn">
                            进球
                        </button>
                        <div class="number-control">
                            <button class="control-btn" onclick="updateStats('${player}', 'goal', -1)">-</button>
                            <span class="stats-count" id="${player}-goals">0</span>
                            <button class="control-btn" onclick="updateStats('${player}', 'goal', 1)">+</button>
                        </div>
                    </div>
                    <div class="stats-control">
                        <button class="stats-btn assist-btn">
                            助攻
                        </button>
                        <div class="number-control">
                            <button class="control-btn" onclick="updateStats('${player}', 'assist', -1)">-</button>
                            <span class="stats-count" id="${player}-assists">0</span>
                            <button class="control-btn" onclick="updateStats('${player}', 'assist', 1)">+</button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // 打开数据输入弹窗
    function openStatsModal(player, action) {
        currentPlayer = player;
        currentAction = action;
        const modal = document.getElementById('statsModal');
        const label = document.getElementById('modalLabel');
        const input = document.getElementById('statsInput');
        
        label.textContent = `${player} 的${action === 'goal' ? '进球' : '助攻'}数：`;
        input.value = document.getElementById(`${player}-${action}s`).textContent;
        
        modal.style.display = 'block';
    }

    // 关闭弹窗
    function closeModal() {
        document.getElementById('statsModal').style.display = 'none';
    }

    // 保存数据
    function saveStats() {
        const input = document.getElementById('statsInput');
        const value = parseInt(input.value) || 0;
        const countElement = document.getElementById(`${currentPlayer}-${currentAction}s`);
        countElement.textContent = value;
        closeModal();
    }

    // 修改 window.onclick 事件处理函数
    window.onclick = function(event) {
        const statsModal = document.getElementById('statsModal');
        const finalStatsModal = document.getElementById('finalStatsModal');
        
        // 点击弹窗外部时关闭对应的弹窗
        if (event.target === statsModal) {
            statsModal.style.display = 'none';
        } else if (event.target === finalStatsModal) {
            finalStatsModal.style.display = 'none';
        }
    }

    // 格式化日期
    function formatDate(dateString) {
        const date = new Date(dateString);
        return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    }

    // 显示最终统计结果
    function showFinalStats() {
        try {
            // 验证数据
            if (!validateMatchData()) {
                return;
            }

            const modal = document.getElementById('finalStatsModal');
            
            // 获取并显示比分
            const whiteScore = document.getElementById('whiteTeamScore').value;
            const blueScore = document.getElementById('blueTeamScore').value;
            document.getElementById('finalWhiteScore').textContent = whiteScore;
            document.getElementById('finalBlueScore').textContent = blueScore;
            
            // 收集白队数据
            const whiteTeamData = collectTeamStats('whiteTeamStats');
            displayFinalStats('whiteTeamGoals', 'whiteTeamAssists', whiteTeamData);
            
            // 收集蓝队数据
            const blueTeamData = collectTeamStats('blueTeamStats');
            displayFinalStats('blueTeamGoals', 'blueTeamAssists', blueTeamData);
            
            // 显示弹窗
            modal.style.display = 'block';
        } catch (error) {
            alert(error.message);
        }
    }

    // 收集队统计数据
    function collectTeamStats(teamStatsId) {
        const stats = {
            goals: [],
            assists: []
        };
        
        const playerItems = document.getElementById(teamStatsId).children;
        
        Array.from(playerItems).forEach(item => {
            const playerName = item.querySelector('.player-name').textContent;
            const goals = parseInt(item.querySelector('[id$="-goals"]').textContent) || 0;
            const assists = parseInt(item.querySelector('[id$="-assists"]').textContent) || 0;
            
            // 只记录有进球或助攻的球员
            if (goals > 0) {
                stats.goals.push({ 
                    player: playerName, 
                    count: goals 
                });
            }
            if (assists > 0) {
                stats.assists.push({ 
                    player: playerName, 
                    count: assists 
                });
            }
        });
        
        return stats;
    }

    // 显示最终统计数据
    function displayFinalStats(goalsListId, assistsListId, data) {
        // 显示进球数据
        const goalsList = document.getElementById(goalsListId);
        goalsList.innerHTML = data.goals
            .sort((a, b) => b.count - a.count)
            .map(item => `<li>${item.player} ${item.count}球</li>`)
            .join('') || '<li>无</li>';
        
        // 显示助攻数据
        const assistsList = document.getElementById(assistsListId);
        assistsList.innerHTML = data.assists
            .sort((a, b) => b.count - a.count)
            .map(item => `<li>${item.player} ${item.count}次</li>`)
            .join('') || '<li>无</li>';
    }

    // 关闭最终统计弹窗
    function closeFinalStatsModal() {
        document.getElementById('finalStatsModal').style.display = 'none';
    }

    // 修改 saveFinalStats 函数
    async function saveFinalStats() {
        try {
            // 获取比分
            const whiteScore = parseInt(document.getElementById('whiteTeamScore').value) || 0;
            const blueScore = parseInt(document.getElementById('blueTeamScore').value) || 0;

            // 获取比赛 ID
            const matchData = JSON.parse(localStorage.getItem('currentMatch'));
            const matchId = matchData?.matchId;

            if (!matchId) {
                throw new Error('未找到比赛信息');
            }

            console.log('开始保存比赛数据...');
            console.log('比分:', { white: whiteScore, blue: blueScore });

            // 收集统计数据
            const whiteTeamStats = collectTeamStats('whiteTeamStats');
            const blueTeamStats = collectTeamStats('blueTeamStats');

            console.log('白队统计:', whiteTeamStats);
            console.log('蓝队统计:', blueTeamStats);

            // 1. 首先更新比赛结果
            const { error: matchError } = await supabaseClient
                .from('matches')
                .update({
                    white_score: whiteScore,
                    blue_score: blueScore,
                    status: 'completed'
                })
                .eq('id', matchId);

            if (matchError) {
                console.error('更新比赛结果失败:', matchError);
                throw matchError;
            }

            console.log('比赛结果更新成功');

            // 2. 删除已有的统计数据
            const { error: deleteError } = await supabaseClient
                .from('match_stats')
                .delete()
                .eq('match_id', matchId);

            if (deleteError) {
                console.error('删除旧统计数据失败:', deleteError);
                throw deleteError;
            }

            console.log('旧统计数据删除成功');

            // 3. 准备新的统计数据
            const statsData = [];

            // 处理白队数据
            whiteTeamStats.goals.forEach(stat => {
                statsData.push({
                    match_id: matchId,
                    player_name: stat.player,
                    team: 'white',
                    goals: stat.count,
                    assists: 0
                });
            });

            whiteTeamStats.assists.forEach(stat => {
                const existingPlayer = statsData.find(
                    s => s.player_name === stat.player && s.team === 'white'
                );
                if (existingPlayer) {
                    existingPlayer.assists = stat.count;
                } else {
                    statsData.push({
                        match_id: matchId,
                        player_name: stat.player,
                        team: 'white',
                        goals: 0,
                        assists: stat.count
                    });
                }
            });

            // 处理蓝队数据
            blueTeamStats.goals.forEach(stat => {
                statsData.push({
                    match_id: matchId,
                    player_name: stat.player,
                    team: 'blue',
                    goals: stat.count,
                    assists: 0
                });
            });

            blueTeamStats.assists.forEach(stat => {
                const existingPlayer = statsData.find(
                    s => s.player_name === stat.player && s.team === 'blue'
                );
                if (existingPlayer) {
                    existingPlayer.assists = stat.count;
                } else {
                    statsData.push({
                        match_id: matchId,
                        player_name: stat.player,
                        team: 'blue',
                        goals: 0,
                        assists: stat.count
                    });
                }
            });

            console.log('准备插入的统计数据:', statsData);

            // 4. 插入新的统计数据
            if (statsData.length > 0) {
                const { error: insertError } = await supabaseClient
                    .from('match_stats')
                    .insert(statsData);

                if (insertError) {
                    console.error('插入统计数据失败:', insertError);
                    throw insertError;
                }

                console.log('统计数据保存成功');
            }

            // 5. 清除当前比赛数据并跳转
            localStorage.removeItem('currentMatch');
            console.log('本地数据已清除');

            alert('比赛数据保存成功！');
            window.location.href = 'results.html';

        } catch (error) {
            console.error('保存失败:', error);
            alert('保存失败：' + error.message);
        }
    }

    // 在队伍名单下显示统计结果
    function displayTeamFinalStats(containerId, data, score) {
        const container = document.getElementById(containerId);
        const content = container.querySelector('.stats-content');
        
        let html = `<div class="stats-category">
            <h5>比分</h5>
            <ul><li>${score} 分</li></ul>
        </div>`;
        
        if (data.goals.length > 0) {
            html += `<div class="stats-category">
                <h5>进球</h5>
                <ul>
                    ${data.goals.sort((a, b) => b.count - a.count)
                        .map(item => `<li>${item.name} ${item.count}个</li>`)
                        .join('')}
                </ul>
            </div>`;
        }
        
        if (data.assists.length > 0) {
            html += `<div class="stats-category">
                <h5>助攻</h5>
                <ul>
                    ${data.assists.sort((a, b) => b.count - a.count)
                        .map(item => `<li>${item.name} ${item.count}次</li>`)
                        .join('')}
                </ul>
            </div>`;
        }
        
        content.innerHTML = html;
        container.style.display = 'block';
    }

    // 修改 removePlayer 函数
    async function removePlayer(playerName, teamType) {
        try {
            const matchData = JSON.parse(localStorage.getItem('currentMatch'));
            const matchId = matchData?.matchId;

            if (!matchId) {
                throw new Error('没有找到比赛信息');
            }

            // 从数据库中删除球员
            const { error: deleteError } = await supabaseClient
                .from('match_players')
                .delete()
                .eq('match_id', matchId)
                .eq('player_name', playerName)
                .eq('team', teamType);

            if (deleteError) {
                throw deleteError;
            }

            // 更新本地存储中的数据
            if (teamType === 'white') {
                matchData.teams.white.players = matchData.teams.white.players.filter(name => name !== playerName);
            } else {
                matchData.teams.blue.players = matchData.teams.blue.players.filter(name => name !== playerName);
            }
            localStorage.setItem('currentMatch', JSON.stringify(matchData));

            // 更新显示
            displayTeamList(`${teamType}TeamList`, matchData.teams[teamType].players);
            initializeStatsSection(`${teamType}TeamStats`, matchData.teams[teamType].players);

            alert('队员删除成功');

        } catch (error) {
            console.error('删除队员失败:', error);
            alert('删除队员失败：' + error.message);
        }
    }

    // 添加数据验证函数
    function validateMatchData() {
        const whiteScore = document.getElementById('whiteTeamScore').value;
        const blueScore = document.getElementById('blueTeamScore').value;

        // 验证比分
        if (!whiteScore || !blueScore) {
            throw new Error('请输入双方比分');
        }

        // 验证比分是否为数字
        if (isNaN(whiteScore) || isNaN(blueScore)) {
            throw new Error('比分必须是数字');
        }

        // 验证比分是否为非负数
        if (whiteScore < 0 || blueScore < 0) {
            throw new Error('比分不能为负数');
        }

        return true;
    }

    // 添加 updateStats 函数
    function updateStats(player, type, change) {
        const countElement = document.getElementById(`${player}-${type}s`);
        let currentValue = parseInt(countElement.textContent) || 0;
        currentValue += change;
        
        // 确保数值不小于0
        if (currentValue < 0) {
            currentValue = 0;
        }
        
        countElement.textContent = currentValue;
    }
    
    // 汉堡菜单控制函数
    function toggleMenu() {
        const navMenu = document.querySelector('.nav-menu');
        const hamburger = document.querySelector('.hamburger');
        
        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');
    }
    
    // 点击菜单项或页面其他区域时关闭菜单
    document.addEventListener('DOMContentLoaded', function() {
        // 点击菜单项时关闭菜单
        document.querySelectorAll('.nav-menu a').forEach(link => {
            link.addEventListener('click', () => {
                document.querySelector('.nav-menu').classList.remove('active');
                document.querySelector('.hamburger').classList.remove('active');
            });
        });
        
        // 点击页面其他区域时关闭菜单
        document.addEventListener('click', function(e) {
            const navMenu = document.querySelector('.nav-menu');
            const hamburger = document.querySelector('.hamburger');
            
            if (!navMenu.contains(e.target) && !hamburger.contains(e.target)) {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            }
        });
    });
    </script>
</body>
</html>