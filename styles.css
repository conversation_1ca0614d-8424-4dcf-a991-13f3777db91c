/* CSS v2.5 - 足球比赛记录系统样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 桌面版隐藏底部导航栏 */
.mobile-bottom-nav {
    display: none;
}

/* 移动端底部导航栏 */
@media screen and (max-width: 768px) {
    /* 隐藏原有的导航菜单和汉堡按钮 */
    .nav-menu {
        display: none !important;
    }
    
    .hamburger {
        display: none !important;
    }
    
    /* 创建底部导航栏 */
    .mobile-bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #1E88E5;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 0.5rem 0;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        height: 60px;
    }
    
    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: rgba(255,255,255,0.7);
        transition: all 0.3s ease;
        padding: 0.3rem;
        border-radius: 8px;
        min-width: 50px;
    }
    
    .mobile-nav-item.active,
    .mobile-nav-item:hover {
        color: white;
        background-color: rgba(255,255,255,0.1);
    }
    
    .mobile-nav-icon {
        font-size: 1.2rem;
        margin-bottom: 0.2rem;
    }
    
    .mobile-nav-text {
        font-size: 0.7rem;
        text-align: center;
    }
    
    /* 为底部导航留出空间 */
    body {
        padding-bottom: 60px;
    }
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    background-color: #1E88E5;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    height: 60px;
    opacity: 1;
    backdrop-filter: none;
}

.nav-brand {
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0 2rem;
    white-space: nowrap;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: stretch;
    height: 100%;
}

.nav-menu > li {
    position: relative;
    height: 100%;
    display: flex;
    align-items: stretch;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    transition: all 0.3s;
    white-space: nowrap;
    position: relative;
}

.nav-menu a:hover {
    background-color: rgba(255,255,255,0.1);
}

.nav-menu a.active {
    background-color: rgba(255,255,255,0.2);
}

/* 下拉菜单样式 */
.dropdown {
    position: relative;
    height: 100%;
}

.dropdown > span {
    color: white;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
}

.dropdown > span::after {
    content: '▼';
    font-size: 0.8em;
    margin-left: 0.5rem;
    transition: transform 0.3s;
}

.dropdown:hover > span {
    background-color: rgba(255,255,255,0.1);
}

.dropdown:hover > span::after {
    transform: rotate(180deg);
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: white;
    min-width: 200px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 0 0 4px 4px;
    z-index: 1001;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    color: #333;
    padding: 0.8rem 1.5rem;
    border-bottom: 1px solid #eee;
    display: block;
}

.dropdown-content a:last-child {
    border-bottom: none;
}

.dropdown-content a:hover {
    background-color: #f5f5f5;
    color: #1E88E5;
}

.dropdown-content a.active {
    background-color: #e3f2fd;
    color: #1E88E5;
}

/* 汉堡菜单按钮 */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    margin-right: 1rem;
    align-self: center;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* 响应式导航栏 - 统一移动端样式 */
@media screen and (max-width: 1200px) and (min-width: 769px) {
    .nav-menu > li {
        display: none;
    }
    
    .hamburger {
        display: flex !important;
    }
}

/* 移动端完全隐藏原有导航菜单 */
@media screen and (max-width: 768px) {
    .navbar {
        flex-direction: row;
        height: 60px;
        padding: 0;
        justify-content: center;
        align-items: center;
        background-color: #1E88E5 !important;
        opacity: 1 !important;
        backdrop-filter: none !important;
    }

    .nav-brand {
        padding: 0 1rem;
        justify-content: center;
        width: 100%;
        text-align: center;
    }

    /* 完全隐藏汉堡菜单和原有导航 */
    .hamburger {
        display: none !important;
    }

    .nav-menu {
        display: none !important;
    }

    .nav-menu > li {
        display: none !important;
    }

    .dropdown {
        display: none !important;
    }

    .dropdown-content {
        display: none !important;
    }
}

/* 主容器样式 */
.container {
    max-width: 1400px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* 移动端主容器样式修正 */
@media screen and (max-width: 768px) {
    .container {
        margin-top: 1rem;
        padding-top: 1rem;
    }
}

/* 页脚样式 */
.footer {
    text-align: center;
    padding: 2rem;
    background-color: #f8f9fa;
    margin-top: 3rem;
    border-top: 1px solid #eee;
}

/* 比赛信息区域样式 */
.match-info-section {
    margin-bottom: 2rem;
}

.match-info-card {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.9rem;
    color: #666;
}

.form-group input,
.form-group select {
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    background-color: #f8f9fa;
}

/* 球员选择区域样式 */
.player-selection-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* 队伍容器样式 */
.teams-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.team {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.team h3 {
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
    color: #1E88E5;
}

.team ul {
    min-height: 100px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    list-style: none;
}

.team li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.8rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* 球员列表样式 */
.player-lists {
    margin-top: 2rem;
}

.player-list, 
.guest-section {
    margin-bottom: 3rem;
}

.section-title {
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
    color: #333;
    font-size: 1.2rem;
}

.player-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.8rem;
    margin: 0;
    padding: 0;
    list-style: none;
}

.player-grid li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.player-name {
    flex: 1;
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

.button-group {
    display: flex;
    gap: 0.3rem;
}

/* 按钮样式 */
.team-btn {
    padding: 0.3rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s;
    background-color: #f8f9fa;
    color: #666;
}

.team-btn:hover:not(:disabled) {
    background-color: #eee;
}

.team-btn.white.selected {
    background-color: #43A047;
    color: white;
    border-color: #43A047;
}

.team-btn.blue.selected {
    background-color: #1E88E5;
    color: white;
    border-color: #1E88E5;
}

/* 删除按钮样式 */
.delete-btn {
    padding: 0.2rem 0.4rem;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s;
    margin-left: 0.3rem;
}

.delete-btn:hover {
    background-color: #c82333;
}

/* 提交按钮样式 */
.submit-section {
    margin-top: 2rem;
    text-align: center;
}

.btn-primary {
    padding: 0.8rem 2rem;
    background-color: #1E88E5;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s;
}

.btn-primary:hover {
    background-color: #1976D2;
}

/* 响应式设计 */
@media (max-width: 1600px) {
    .player-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

@media (max-width: 1400px) {
    .player-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 1200px) {
    .player-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .match-info-card {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .teams-container {
        grid-template-columns: 1fr;
    }
    
    .player-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .match-info-card {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .player-grid {
        grid-template-columns: 1fr;
    }
    
    .navbar {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* 年度选择器区域样式 */
.year-selector-section {
    margin-bottom: 2rem;
}

.year-selector {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 1rem;
}

.year-btn {
    padding: 10px 30px;
    border: none;
    border-radius: 20px;
    background: #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.year-btn.active {
    background: #2196F3;
    color: white;
}

/* 年度内容区域样式 */
.year-content {
    display: none;
}

.year-content.active {
    display: block;
}

/* iframe样式 */
.year-content iframe {
    width: 100%;
    height: calc(100vh - 300px);
    border: none;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 统计卡片样式 */
.stats-cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.stats-card {
    flex: 1;
    min-width: 250px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-card h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.stats-number {
    font-size: 24px;
    font-weight: bold;
    color: #2196F3;
    margin: 0;
}

/* 图表区域样式 */
.stats-charts {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container {
    width: 100%;
    height: 400px;
}

/* 球员统计表格样式 */
.stats-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.stats-tabs {
    margin-bottom: 20px;
    border-bottom: 2px solid #eee;
    display: flex;
    justify-content: center;
    gap: 20px;
}

.tab-btn {
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 16px;
    position: relative;
}

.tab-btn.active {
    color: #1E88E5;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #1E88E5;
}

.stats-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    background: #f8f9fa;
    padding: 15px;
    font-weight: bold;
    border-bottom: 2px solid #eee;
}

.player-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    align-items: center;
}

.player-row:hover {
    background: #f8f9fa;
}

.col {
    padding: 5px;
    display: flex;
    align-items: center;
}

.edit-btn {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    margin-left: 10px;
    font-size: 12px;
    color: #666;
}

.edit-btn:hover {
    background: #f0f0f0;
    border-color: #1E88E5;
    color: #1E88E5;
}

.add-player-row {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
}

.add-player-row button {
    padding: 10px 20px;
    background: #1E88E5;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.add-player-row button:hover {
    background: #1976D2;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .table-header,
    .player-row {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
    
    .col.actions {
        display: none;
    }
}

@media (max-width: 480px) {
    .table-header,
    .player-row {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .col.stats {
        display: none;
    }
}

/* 添加排序相关样式 */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sortable:hover {
    background-color: #f0f0f0;
}

/* 确保表头文字和图标在同一行 */
.table-header .col {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 修改榜单选择器样式 */
.stats-boards {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.board-btn {
    padding: 0;
    border: none;
    border-radius: 25px;
    background: #f0f0f0;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.board-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1E88E5, #1976D2);
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1;
}

.board-btn span {
    position: relative;
    z-index: 2;
    color: #666;
    transition: color 0.3s;
    padding: 15px 30px;
    display: block;
}

.board-btn.active {
    color: white;
}

.board-btn.active::before {
   /* opacity: 1; */
}

.board-btn.active span {
    z-index: 3;
}

/* 表格样式优化 */
.stats-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 80px 2fr 1fr;
    background: #f8f9fa;
    padding: 15px;
    font-weight: bold;
    border-bottom: 2px solid #eee;
}

.player-row {
    display: grid;
    grid-template-columns: 80px 2fr 1fr;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    align-items: center;
    transition: background-color 0.3s;
}

.player-row:hover {
    background: #f8f9fa;
}

/* 排名样式优化 */
.rank {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto;
    background: #f0f0f0;
    color: #666;
}

.top-1 .rank {
    background: linear-gradient(45deg, #FFD700, #FFA000);
    color: white;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.top-2 .rank {
    background: linear-gradient(45deg, #C0C0C0, #9E9E9E);
    color: white;
    box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.top-3 .rank {
    background: linear-gradient(45deg, #CD7F32, #8D6E63);
    color: white;
    box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

/* 数值显示优化 */
.value {
    font-weight: bold;
    color: #1E88E5;
    font-size: 16px;
}

/* 编辑按钮优化 */
.edit-btn {
    padding: 4px 12px;
    border: 1px solid #ddd;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    margin-left: 10px;
    font-size: 12px;
    color: #666;
    transition: all 0.3s;
}

.edit-btn:hover {
    background: #1E88E5;
    color: white;
    border-color: #1E88E5;
}

/* 同样修改标签页按钮样式 */
.tab-btn {
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 16px;
    position: relative;
}

.tab-btn span {
    position: relative;
    padding: 10px 20px;
    display: block;
}