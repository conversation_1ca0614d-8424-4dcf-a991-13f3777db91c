<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>球员统计 - 足球队数据统计系统</title>
    <link rel="stylesheet" href="styles.css?v=2.1">
    <style>
        .stats-section h2 {
            color: #333;
            margin-bottom: 2rem;
            font-size: 1.8rem;
        }

        .stats-boards {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .board-btn {
            background: #f0f0f0;
            color: #666;
            border: none;
            padding: 0.8rem 2rem;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s;
            border-radius: 100px;
        }

        .board-btn:hover {
            background: #e8e8e8;
        }

        .board-btn.active {
            background: #2196F3;
            color: #fff;
        }

        .stats-table {
            width: 100%;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .table-header, .player-row {
            display: grid;
            grid-template-columns: 80px 1.5fr repeat(3, 1fr);
            padding: 1.2rem 1.5rem;
            align-items: center;
        }

        .table-header {
            background: #f8f9fa;
            font-weight: 600;
            color: #444;
        }

        .table-header .col {
            cursor: pointer;
            user-select: none;
            white-space: nowrap;
        }

        .table-header .col:hover {
            color: #1E88E5;
        }

        .table-header .col.sorted:after {
            content: '';
            display: inline-block;
            width: 0;
            height: 0;
            margin-left: 8px;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
        }

        .table-header .col.sorted.asc:after {
            border-bottom: 4px solid #1E88E5;
        }

        .table-header .col.sorted.desc:after {
            border-top: 4px solid #1E88E5;
        }

        .player-row {
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .player-row:last-child {
            border-bottom: none;
        }

        .player-row:hover {
            background: #f8f9fa;
        }

        .col {
            display: flex;
            align-items: center;
        }

        .col.rank {
            color: #666;
            font-weight: 500;
        }

        .col.name {
            font-weight: 500;
        }

        .col.value {
            justify-content: center;
            color: #1E88E5;
            font-weight: 600;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #666;
            background: #f8f9fa;
            border-radius: 12px;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">丛莱梅蔬果足球队</div>
        <ul class="nav-menu">
            <li><a href="index.html">首页</a></li>
            <li><a href="results.html">比赛结果</a></li>
            <li><a href="stats.html" class="active">球员统计</a></li>
            <li><a href="winrate.html">胜率统计</a></li>
        </ul>
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </nav>

    <main class="container">
        <section class="stats-section">
            <h2>2025年数据统计</h2>
            <div class="stats-content">
                <!-- 这里将由 JavaScript 动态生成内容 -->
            </div>
        </section>
    </main>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <a href="index.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-home"></i></div>
            <div class="mobile-nav-text">首页</div>
        </a>
        <a href="results.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-trophy"></i></div>
            <div class="mobile-nav-text">结果</div>
        </a>
        <a href="stats.html" class="mobile-nav-item active">
            <div class="mobile-nav-icon"><i class="fas fa-chart-bar"></i></div>
            <div class="mobile-nav-text">统计</div>
        </a>
        <a href="winrate.html" class="mobile-nav-item">
            <div class="mobile-nav-icon"><i class="fas fa-percentage"></i></div>
            <div class="mobile-nav-text">胜率</div>
        </a>
    </nav>

    <footer class="footer">
        <p>© 2013-2025 丛莱梅蔬果足球队</p>
    </footer>

    <!-- 确保按照正确的顺序引入脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/dist/umd/supabase.min.js"></script>
    <script src="js/stats.js"></script>
    <script>
        // 等待 DOM 和 Supabase 都加载完成
        window.addEventListener('load', function() {
            if (typeof supabase === 'undefined') {
                console.error('Supabase 未加载');
                return;
            }
            console.log('Supabase 已加载');
            window.playerStats = new PlayerStats();
        });

        // 汉堡菜单切换
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const navMenu = document.querySelector('.nav-menu');
            
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        }

        // 点击菜单项时关闭菜单
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-menu a');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    const hamburger = document.querySelector('.hamburger');
                    const navMenu = document.querySelector('.nav-menu');
                    hamburger.classList.remove('active');
                    navMenu.classList.remove('active');
                });
            });
        });

        // 点击页面其他地方时关闭菜单
        document.addEventListener('click', function(event) {
            const navbar = document.querySelector('.navbar');
            const hamburger = document.querySelector('.hamburger');
            const navMenu = document.querySelector('.nav-menu');
            
            if (!navbar.contains(event.target) && navMenu.classList.contains('active')) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    </script>
</body>
</html>